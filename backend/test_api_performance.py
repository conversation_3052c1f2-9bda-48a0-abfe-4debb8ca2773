#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API性能检测脚本 - 定位慢接口和数据源
"""

import requests
import time
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Tuple

class APIPerformanceTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.timeout = 15  # 15秒超时
        
    def test_single_api(self, endpoint: str) -> Tuple[str, float, bool, str]:
        """测试单个API接口"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            response = requests.get(url, timeout=self.timeout)
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success', False):
                    return endpoint, elapsed, True, f"✅ 成功 - 数据量: {len(str(data))}"
                else:
                    return endpoint, elapsed, False, f"❌ 接口返回失败: {data.get('message', '未知错误')}"
            else:
                return endpoint, elapsed, False, f"❌ HTTP {response.status_code}"
                
        except requests.exceptions.Timeout:
            elapsed = time.time() - start_time
            return endpoint, elapsed, False, f"⏰ 超时 (>{self.timeout}s)"
        except requests.exceptions.ConnectionError:
            elapsed = time.time() - start_time
            return endpoint, elapsed, False, "🔌 连接失败"
        except Exception as e:
            elapsed = time.time() - start_time
            return endpoint, elapsed, False, f"💥 异常: {str(e)}"

    def test_all_apis(self) -> List[Tuple[str, float, bool, str]]:
        """并发测试所有API接口"""
        endpoints = [
            "/api/market/indices",           # 指数数据
            "/api/market/hot-stocks",        # 热门股票
            "/api/market/sectors",           # 板块数据
            "/api/market/statistics",        # 市场统计
            "/api/dashboard/overview",       # 监控大屏总览
            "/health",                       # 健康检查
        ]
        
        print("🚀 开始API性能测试...")
        print("=" * 60)
        
        results = []
        with ThreadPoolExecutor(max_workers=6) as executor:
            futures = [executor.submit(self.test_single_api, endpoint) for endpoint in endpoints]
            for future in futures:
                results.append(future.result())
        
        return results

    def analyze_results(self, results: List[Tuple[str, float, bool, str]]):
        """分析测试结果"""
        print("\n📊 API性能分析报告")
        print("=" * 60)
        
        # 按耗时排序
        sorted_results = sorted(results, key=lambda x: x[1], reverse=True)
        
        slow_apis = []
        fast_apis = []
        failed_apis = []
        
        for endpoint, elapsed, success, message in sorted_results:
            status_icon = "🟢" if success and elapsed < 2 else "🟡" if success and elapsed < 5 else "🔴"
            print(f"{status_icon} {endpoint:<25} | {elapsed:6.2f}s | {message}")
            
            if not success:
                failed_apis.append((endpoint, elapsed, message))
            elif elapsed > 3:
                slow_apis.append((endpoint, elapsed))
            else:
                fast_apis.append((endpoint, elapsed))
        
        print("\n" + "=" * 60)
        print("🎯 性能总结:")
        print(f"   快速接口 (<3s):  {len(fast_apis)} 个")
        print(f"   慢速接口 (>3s):  {len(slow_apis)} 个")
        print(f"   失败接口:        {len(failed_apis)} 个")
        
        if slow_apis:
            print(f"\n🐌 最慢的接口: {slow_apis[0][0]} ({slow_apis[0][1]:.2f}s)")
            
        if failed_apis:
            print(f"\n💥 失败的接口:")
            for endpoint, elapsed, message in failed_apis:
                print(f"   {endpoint}: {message}")

    def run_continuous_test(self, rounds: int = 3):
        """连续多轮测试"""
        print(f"🔄 连续测试 {rounds} 轮，观察性能稳定性...")
        
        all_round_results = []
        for round_num in range(1, rounds + 1):
            print(f"\n--- 第 {round_num} 轮测试 ---")
            results = self.test_all_apis()
            all_round_results.append(results)
            self.analyze_results(results)
            
            if round_num < rounds:
                print("\n⏱️  等待 5 秒后开始下一轮...")
                time.sleep(5)
        
        # 计算平均性能
        self.calculate_average_performance(all_round_results)

    def calculate_average_performance(self, all_results: List[List[Tuple[str, float, bool, str]]]):
        """计算平均性能"""
        print(f"\n📈 {len(all_results)} 轮测试平均性能:")
        print("=" * 60)
        
        endpoint_stats = {}
        
        for round_results in all_results:
            for endpoint, elapsed, success, message in round_results:
                if endpoint not in endpoint_stats:
                    endpoint_stats[endpoint] = []
                if success:  # 只统计成功的请求
                    endpoint_stats[endpoint].append(elapsed)
        
        for endpoint, times in endpoint_stats.items():
            if times:
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                status_icon = "🟢" if avg_time < 2 else "🟡" if avg_time < 5 else "🔴"
                print(f"{status_icon} {endpoint:<25} | 平均: {avg_time:6.2f}s | 最快: {min_time:6.2f}s | 最慢: {max_time:6.2f}s")

def main():
    """主函数"""
    tester = APIPerformanceTester()
    
    print("🎯 AI交易系统 - API性能诊断工具")
    print("=" * 60)
    
    # 首先检查服务是否启动
    try:
        response = requests.get(f"{tester.base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未正常启动，请先运行 ./start_services.sh")
            return
    except:
        print("❌ 无法连接到后端服务，请确认服务已启动")
        return
    
    print("✅ 后端服务连接正常")
    
    # 运行性能测试
    tester.run_continuous_test(rounds=3)
    
    print("\n🎉 性能测试完成！")
    print("\n💡 优化建议:")
    print("   1. 如果某个接口持续 >5秒，检查对应的数据源")
    print("   2. 如果 /api/dashboard/overview 很慢，说明综合数据获取有问题")
    print("   3. 如果 /api/market/indices 很慢，说明新浪财经接口有问题")
    print("   4. 建议为慢接口增加缓存时间或异步预热")

if __name__ == "__main__":
    main() 