#!/usr/bin/env python3
import asyncio, json, sys, os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from services.market_service import MarketService
async def main():
    ms = MarketService()
    data = {
        "global_indices": await ms.get_global_indices(),
        "hot_stocks": await ms.get_hot_stocks(),
        "commodities": await ms.get_commodities_data(),
        "forex": await ms.get_forex_data(),
        "sectors": await ms.get_sectors_data(),
        "market_stats": await ms.get_market_statistics(),
        "risk_indicators": await ms.get_risk_indicators(),
    }
    print(json.dumps(data)[:500])
if __name__ == "__main__":
    asyncio.run(main()) 