/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:160: DeprecationWarning: `regex` has been deprecated, please use `pattern` instead
  period: str = Query("1d", regex="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:191: DeprecationWarning: `regex` has been deprecated, please use `pattern` instead
  async def get_hot_stocks(stock_type: str = Query("gainers", regex="^(gainers|losers|volume|turnover)$")):
/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:623: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [38719] using WatchFiles
Redis连接成功
Redis连接成功
INFO:     Started server process [38725]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:64737 - "GET /health HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.78it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.93it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.45it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.56it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.05it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.50it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.83it/s]
100%|██████████| 8/8 [00:01<00:00,  5.68it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64756 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64757 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.71it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.47it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.02it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.26it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.63it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.91it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.17it/s]
100%|██████████| 8/8 [00:01<00:00,  6.79it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64773 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:64865 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:64866 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  3.90it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.50it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.26it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.22it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.73it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.24it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.66it/s]
100%|██████████| 8/8 [00:01<00:00,  6.40it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64868 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.44it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.56it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.26it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.04it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.10it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.69it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.46it/s]
100%|██████████| 8/8 [00:01<00:00,  5.32it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64865 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:64870 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:64870 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64865 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:64868 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:08,  1.18s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.58it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.43it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.26it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.59it/s]
 75%|███████▌  | 6/8 [00:03<00:01,  1.67it/s]
 88%|████████▊ | 7/8 [00:03<00:00,  2.19it/s]
100%|██████████| 8/8 [00:03<00:00,  2.51it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64969 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64970 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.13it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.72it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.62it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.58it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.32it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.42it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.91it/s]
100%|██████████| 8/8 [00:01<00:00,  4.83it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64970 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64969 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.81it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.13it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.09it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.73it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.02it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.19it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.27it/s]
100%|██████████| 8/8 [00:01<00:00,  6.99it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65223 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65225 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.90it/s]
 25%|██▌       | 2/8 [00:01<00:04,  1.31it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.03it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.74it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.37it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  3.91it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.89it/s]
100%|██████████| 8/8 [00:03<00:00,  1.87it/s]
                                             
获取外汇数据失败: IncompleteRead(786139 bytes read, 309436 more expected)
成功获取3个指数数据
INFO:     127.0.0.1:65336 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65337 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.81it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.77it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.87it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.00it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.72it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.57it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.61it/s]
100%|██████████| 8/8 [00:01<00:00,  5.35it/s]
                                             
获取成交额排行数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
成功获取3个指数数据
INFO:     127.0.0.1:65336 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.29it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.94it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.42it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.68it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.73it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.30it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.55it/s]
100%|██████████| 8/8 [00:01<00:00,  5.19it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65473 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65337 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:65474 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:03,  1.93it/s]
 25%|██▌       | 2/8 [00:00<00:02,  2.82it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.64it/s]
 50%|█████     | 4/8 [00:01<00:00,  4.26it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.16it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.01it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.00it/s]
100%|██████████| 8/8 [00:02<00:00,  4.74it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65529 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65530 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49776 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49778 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49776 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:49780 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49782 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49786 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49787 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49788 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:49776 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49780 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49782 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49786 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49787 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49788 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49776 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:08,  1.24s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.61it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.22it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.78it/s]
 62%|██████▎   | 5/8 [00:02<00:00,  3.43it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  3.81it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  4.26it/s]
100%|██████████| 8/8 [00:02<00:00,  4.37it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49780 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:49787 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:49782 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.11it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.25it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.90it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.15it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.26it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.18it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.31it/s]
100%|██████████| 8/8 [00:01<00:00,  4.66it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49787 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49787 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49935 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:49936 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.66it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.58it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.77it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.04it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.37it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.17it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.87it/s]
100%|██████████| 8/8 [00:01<00:00,  5.56it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49972 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49973 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50073 - "GET /health HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:02,  2.59it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.85it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.58it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.03it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.31it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.10it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.53it/s]
100%|██████████| 8/8 [00:01<00:00,  6.29it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50081 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50082 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.63it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.24it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.95it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.67it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.99it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.72it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.52it/s]
100%|██████████| 8/8 [00:01<00:00,  5.23it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50134 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.34it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.85it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.29it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.01it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.64it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.18it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.63it/s]
100%|██████████| 8/8 [00:01<00:00,  4.79it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50158 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50159 - "GET /api/market/sectors HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [38725]
INFO:     Started server process [40010]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.37it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.47it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.43it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.42it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.29it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  3.58it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.20it/s]
100%|██████████| 8/8 [00:01<00:00,  5.13it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50266 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50267 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.67it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.68it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.89it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.29it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.46it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.87it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.08it/s]
100%|██████████| 8/8 [00:01<00:00,  6.74it/s]
                                             
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
成功获取3个指数数据
INFO:     127.0.0.1:50331 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50332 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [40010]
INFO:     Started server process [40051]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.82it/s]
 25%|██▌       | 2/8 [00:01<00:04,  1.31it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.08it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.85it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.55it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.22it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  4.81it/s]
100%|██████████| 8/8 [00:02<00:00,  5.63it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50462 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.47it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.49it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.21it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.62it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.93it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.16it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.35it/s]
100%|██████████| 8/8 [00:01<00:00,  7.02it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50481 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50482 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50520 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50521 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.51it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.50it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.13it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.28it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.31it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.49it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.49it/s]
100%|██████████| 8/8 [00:01<00:00,  7.01it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50523 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.03it/s]
 25%|██▌       | 2/8 [00:00<00:01,  6.00it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.33it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.44it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.39it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.72it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.99it/s]
100%|██████████| 8/8 [00:01<00:00,  6.75it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50520 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50535 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:50565 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50566 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50567 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.32it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.59it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.49it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.32it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.91it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.41it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.81it/s]
100%|██████████| 8/8 [00:01<00:00,  5.62it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50602 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50603 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.57it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.57it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.49it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.53it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.53it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  5.98it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.51it/s]
100%|██████████| 8/8 [00:01<00:00,  6.15it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50757 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50759 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.73it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.70it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.86it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.02it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.40it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.02it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.27it/s]
100%|██████████| 8/8 [00:01<00:00,  5.85it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50809 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50810 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.38it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.30it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.47it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.63it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.67it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.61it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.47it/s]
100%|██████████| 8/8 [00:01<00:00,  6.99it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50916 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50918 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.96it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.87it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.91it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.04it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.14it/s]
 75%|███████▌  | 6/8 [00:02<00:01,  1.81it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.27it/s]
100%|██████████| 8/8 [00:02<00:00,  2.81it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50983 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50984 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.81it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.90it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.94it/s]
 50%|█████     | 4/8 [00:01<00:02,  1.65it/s]
 62%|██████▎   | 5/8 [00:02<00:01,  1.85it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  2.34it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.79it/s]
100%|██████████| 8/8 [00:02<00:00,  3.40it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51081 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.00it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.96it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.69it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.78it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.04it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.58it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.76it/s]
100%|██████████| 8/8 [00:01<00:00,  5.45it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51105 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51106 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:02,  2.71it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.99it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.13it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.65it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.93it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.23it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.64it/s]
100%|██████████| 8/8 [00:01<00:00,  5.57it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51105 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51106 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.86it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.89it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.85it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.57it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.74it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.70it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.79it/s]
100%|██████████| 8/8 [00:01<00:00,  6.40it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51106 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51105 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.75it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.92it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.85it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.53it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.78it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.00it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.05it/s]
100%|██████████| 8/8 [00:02<00:00,  2.02it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51353 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51354 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.60it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.89it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.96it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.92it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.23it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.71it/s]
100%|██████████| 8/8 [00:01<00:00,  5.45it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51377 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51378 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.56it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.77it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.20it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.90it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.17it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.40it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.66it/s]
100%|██████████| 8/8 [00:01<00:00,  5.97it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51379 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.90it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.89it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.39it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.77it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.95it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.78it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.62it/s]
100%|██████████| 8/8 [00:02<00:00,  1.98it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51462 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51463 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.93it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.41it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.61it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.62it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.73it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.73it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.79it/s]
100%|██████████| 8/8 [00:01<00:00,  4.09it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51464 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51571 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.18it/s]
 25%|██▌       | 2/8 [00:01<00:05,  1.19it/s]
 38%|███▊      | 3/8 [00:01<00:02,  1.80it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.33it/s]
 62%|██████▎   | 5/8 [00:02<00:01,  2.97it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  3.57it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.77it/s]
100%|██████████| 8/8 [00:03<00:00,  1.85it/s]
                                             
获取外汇数据失败: IncompleteRead(835291 bytes read, 260284 more expected)
成功获取3个指数数据
INFO:     127.0.0.1:51464 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51571 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51590 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:52019 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52160 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52163 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52166 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52167 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52168 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.04it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.50it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.46it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.51it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.91it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.68it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  3.48it/s]
100%|██████████| 8/8 [00:01<00:00,  4.34it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52019 - "GET /api/market/indices HTTP/1.1" 200 OK
获取成交额排行数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
INFO:     127.0.0.1:52160 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52163 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52166 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52269 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52167 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52168 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52160 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52163 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.91it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.64it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.57it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.55it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.71it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.61it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  1.84it/s]
100%|██████████| 8/8 [00:02<00:00,  2.37it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52019 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52160 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]/Users/<USER>/miniforge3/lib/python3.12/multiprocessing/resource_tracker.py:254: UserWarning: resource_tracker: There appear to be 3 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
