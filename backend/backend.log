/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:161: DeprecationWarning: `regex` has been deprecated, please use `pattern` instead
  period: str = Query("1d", regex="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:192: DeprecationWarning: `regex` has been deprecated, please use `pattern` instead
  async def get_hot_stocks(stock_type: str = Query("gainers", regex="^(gainers|losers|volume|turnover)$")):
/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:633: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [70625] using WatchFiles
Redis连接成功
Redis连接成功
INFO:     Started server process [70633]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:60582 - "GET /health HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.76it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.48it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.72it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.50it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.09it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.93it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.41it/s]
100%|██████████| 8/8 [00:01<00:00,  6.31it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60622 - "GET /api/market/indices HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'services/market_service.py', 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70633]
INFO:     Started server process [70826]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:60808 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:60811 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:60812 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:60816 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:60817 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:60819 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.39it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.63it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.64it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.93it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.93it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  7.14it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  7.11it/s]
100%|██████████| 8/8 [00:01<00:00,  7.48it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60816 - "GET /api/market/indices HTTP/1.1" 200 OK
获取成交额排行数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
INFO:     127.0.0.1:60817 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.53it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.94it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.24it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.85it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.86it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.11it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.99it/s]
100%|██████████| 8/8 [00:01<00:00,  4.34it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60839 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.14it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.99it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.01it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.05it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.06it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.29it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.57it/s]
100%|██████████| 8/8 [00:01<00:00,  7.23it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60811 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.14it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.48it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.00it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.50it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.53it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.64it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.46it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60808 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:60845 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.07it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.33it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.16it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.31it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.08it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.94it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.00it/s]
100%|██████████| 8/8 [00:01<00:00,  5.93it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60808 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.88it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.99it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.94it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.51it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.10it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.67it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.09it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60900 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:60845 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:60811 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:60901 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.05it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.66it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.20it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.51it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.70it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.91it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  7.14it/s]
100%|██████████| 8/8 [00:01<00:00,  7.72it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60902 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:60808 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.90it/s]
 25%|██▌       | 2/8 [00:01<00:04,  1.31it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.08it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.66it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.44it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  4.01it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  4.73it/s]
100%|██████████| 8/8 [00:02<00:00,  5.06it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60912 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:60913 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:60808 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.65it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.82it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.35it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.64it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.82it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.49it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.34it/s]
100%|██████████| 8/8 [00:02<00:00,  2.08it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60914 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.91it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.49it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.85it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.89it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.88it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.69it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.92it/s]
100%|██████████| 8/8 [00:01<00:00,  6.86it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:60962 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:60966 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:60968 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.35it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.55it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.54it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.13it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.64it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.39it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.98it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61002 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:60966 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:60968 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61003 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.27it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.94it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.73it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.43it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.47it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.63it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.81it/s]
100%|██████████| 8/8 [00:01<00:00,  7.51it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61004 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:60968 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61138 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61140 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:61170 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:61138 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61140 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:61004 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61003 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:61184 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:61004 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61186 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:61003 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:02,  2.43it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.16it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.74it/s]
 50%|█████     | 4/8 [00:01<00:00,  4.57it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.58it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.61it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.64it/s]
100%|██████████| 8/8 [00:01<00:00,  5.44it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61204 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61205 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61227 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61229 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.04it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.26it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.11it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.08it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.14it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.55it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.85it/s]
100%|██████████| 8/8 [00:01<00:00,  5.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61252 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61229 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.34it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.20it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.58it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.76it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.93it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.98it/s]
100%|██████████| 8/8 [00:01<00:00,  7.68it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61283 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61284 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.26it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.27it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.38it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.29it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.70it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.00it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.51it/s]
100%|██████████| 8/8 [00:01<00:00,  6.81it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61322 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:61323 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.36it/s]
 25%|██▌       | 2/8 [00:01<00:03,  1.78it/s]
 38%|███▊      | 3/8 [00:01<00:01,  2.72it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.35it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.18it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.84it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.49it/s]
100%|██████████| 8/8 [00:01<00:00,  6.35it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61325 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.37it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.86it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.62it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.20it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.59it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.70it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.48it/s]
100%|██████████| 8/8 [00:01<00:00,  7.21it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61343 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61325 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61344 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61343 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61460 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:61344 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61461 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.13it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.12it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.73it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.24it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.39it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.63it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.82it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61504 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61505 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.28it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.94it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.91it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.38it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.12it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  3.81it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.38it/s]
100%|██████████| 8/8 [00:01<00:00,  4.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61515 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61517 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.00it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.83it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.58it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.21it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.60it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.41it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.19it/s]
100%|██████████| 8/8 [00:01<00:00,  6.11it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61571 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:61515 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.45it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.94it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.34it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.70it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.91it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.06it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.35it/s]
100%|██████████| 8/8 [00:01<00:00,  6.52it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61573 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.59it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.14it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.59it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.30it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.23it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.60it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.37it/s]
100%|██████████| 8/8 [00:01<00:00,  5.49it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61633 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61634 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.44it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.56it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.57it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.41it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.16it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.74it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.43it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61660 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61661 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61661 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61660 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.12it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.05it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.38it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.18it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.88it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.73it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.23it/s]
100%|██████████| 8/8 [00:01<00:00,  6.16it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61802 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61803 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  3.78it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.27it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.63it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.84it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.19it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.32it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.07it/s]
100%|██████████| 8/8 [00:01<00:00,  5.79it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61827 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61828 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61827 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61828 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.07it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.67it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.33it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.65it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.79it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  3.89it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.06it/s]
100%|██████████| 8/8 [00:01<00:00,  4.79it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61907 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:61908 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.40it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.36it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.03it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.00it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.39it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.78it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.04it/s]
100%|██████████| 8/8 [00:01<00:00,  6.46it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61909 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.53it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.03it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.55it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.77it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.88it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.77it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.86it/s]
100%|██████████| 8/8 [00:01<00:00,  6.39it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61941 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:61943 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.69it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.94it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.20it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.94it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.34it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.04it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.44it/s]
100%|██████████| 8/8 [00:01<00:00,  5.69it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61941 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.49it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.92it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.40it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.90it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.26it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.25it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.40it/s]
100%|██████████| 8/8 [00:01<00:00,  6.79it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:61944 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:61943 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:61971 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:61972 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:61973 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.02it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.15it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.56it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.67it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.52it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.71it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.13it/s]
100%|██████████| 8/8 [00:01<00:00,  5.73it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62017 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62018 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.08it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.96it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.17it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.57it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.88it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.65it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.39it/s]
100%|██████████| 8/8 [00:01<00:00,  5.22it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62038 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:62018 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62039 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:62040 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.16it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.95it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.33it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.66it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.73it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.08it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.15it/s]
100%|██████████| 8/8 [00:01<00:00,  6.34it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62073 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62132 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:62133 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:62134 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62132 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.06it/s]
 25%|██▌       | 2/8 [00:01<00:04,  1.26it/s]
 38%|███▊      | 3/8 [00:01<00:02,  1.87it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.61it/s]
 62%|██████▎   | 5/8 [00:02<00:01,  2.97it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  3.37it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.67it/s]
100%|██████████| 8/8 [00:02<00:00,  4.05it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62136 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.02it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.02it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.41it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.64it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.85it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.66it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.80it/s]
100%|██████████| 8/8 [00:01<00:00,  7.26it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62133 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62133 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62203 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:62133 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62204 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:62206 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:62203 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.37it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.57it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.46it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.28it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.78it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.96it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.05it/s]
100%|██████████| 8/8 [00:01<00:00,  5.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62219 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62220 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.93it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.28it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.61it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.51it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.64it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.30it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  3.23it/s]
100%|██████████| 8/8 [00:02<00:00,  3.01it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62288 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62290 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.47it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.68it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.55it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.15it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.50it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.00it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.54it/s]
100%|██████████| 8/8 [00:01<00:00,  5.51it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62392 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62394 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.35it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.49it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.67it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.09it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  7.19it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  7.30it/s]
 88%|████████▊ | 7/8 [00:00<00:00,  7.25it/s]
100%|██████████| 8/8 [00:01<00:00,  7.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62392 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62394 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.53it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.32it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.81it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.46it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.41it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.78it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.15it/s]
100%|██████████| 8/8 [00:01<00:00,  5.89it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62394 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:62392 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.82it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.08it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.43it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.10it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.35it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.76it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.21it/s]
100%|██████████| 8/8 [00:01<00:00,  6.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62394 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.49it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.67it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.54it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.07it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.49it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.76it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.88it/s]
100%|██████████| 8/8 [00:01<00:00,  7.54it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62462 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62392 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.28it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.71it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.43it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.21it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.01it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.60it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.37it/s]
100%|██████████| 8/8 [00:01<00:00,  5.68it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62467 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:62462 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62468 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.98it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.83it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.38it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.86it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.05it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.44it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.97it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62469 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62545 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:62554 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.82it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.05it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.86it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.99it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.96it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.87it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.78it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62561 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:62564 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.28it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.39it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.01it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.60it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.18it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.63it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.39it/s]
100%|██████████| 8/8 [00:01<00:00,  5.53it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62565 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.09it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.21it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.80it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.67it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.67it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.77it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.86it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62568 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62565 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62660 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:62661 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:62568 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62670 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:62671 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.44it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.70it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.39it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.70it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.71it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.69it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.91it/s]
100%|██████████| 8/8 [00:01<00:00,  6.81it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62670 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62568 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62568 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62671 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.83it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.97it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.78it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.72it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.45it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.57it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.97it/s]
100%|██████████| 8/8 [00:01<00:00,  5.74it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62718 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62719 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.34it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.46it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.27it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.85it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.02it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.16it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.50it/s]
100%|██████████| 8/8 [00:01<00:00,  6.64it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62813 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62814 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.28it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.54it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.05it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.52it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.90it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.10it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.55it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62813 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62814 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.16it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.56it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.70it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.78it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.94it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.36it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.57it/s]
100%|██████████| 8/8 [00:01<00:00,  7.26it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62882 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62883 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.35it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.96it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.11it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.36it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.56it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.74it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.71it/s]
100%|██████████| 8/8 [00:01<00:00,  7.26it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62936 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62937 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.20it/s]
 25%|██▌       | 2/8 [00:01<00:05,  1.17it/s]
 38%|███▊      | 3/8 [00:01<00:02,  1.80it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.59it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.35it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  4.09it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  4.71it/s]
100%|██████████| 8/8 [00:02<00:00,  2.87it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62968 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.96it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.04it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.01it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.86it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  7.05it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  7.02it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  7.01it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62937 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62936 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62969 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.50it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.87it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.22it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.83it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.16it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.73it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.39it/s]
100%|██████████| 8/8 [00:01<00:00,  5.49it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:62970 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:62970 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:62969 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:63089 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.02it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.16it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.53it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.74it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.93it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.33it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.70it/s]
100%|██████████| 8/8 [00:01<00:00,  6.16it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63113 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63114 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.21it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.85it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.77it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.15it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.62it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.06it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.41it/s]
100%|██████████| 8/8 [00:01<00:00,  7.21it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63141 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63114 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63113 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63142 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.74it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.92it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.75it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.89it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.00it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.89it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.02it/s]
100%|██████████| 8/8 [00:01<00:00,  5.33it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63205 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63207 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.31it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.90it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.03it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.77it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.10it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.31it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.07it/s]
100%|██████████| 8/8 [00:01<00:00,  6.42it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63230 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63207 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63231 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63235 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.53it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.37it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.74it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.73it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.45it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.61it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.64it/s]
100%|██████████| 8/8 [00:01<00:00,  7.43it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63331 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63332 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.72it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.06it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.37it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.73it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.90it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.92it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.11it/s]
100%|██████████| 8/8 [00:01<00:00,  6.77it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63352 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.92it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.71it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.79it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.71it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.56it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.84it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.93it/s]
100%|██████████| 8/8 [00:01<00:00,  7.57it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63331 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63332 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63354 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.67it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.20it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.93it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.13it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.55it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.08it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.53it/s]
100%|██████████| 8/8 [00:01<00:00,  6.30it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63352 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.02it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.85it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.49it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.81it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.44it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.73it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.31it/s]
100%|██████████| 8/8 [00:01<00:00,  5.95it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63355 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63354 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:63357 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63355 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63359 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:63357 - "GET /api/market/commodities HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.81it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.05it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.92it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.45it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.31it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.72it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.58it/s]
100%|██████████| 8/8 [00:01<00:00,  5.59it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63354 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63360 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:63359 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63355 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63542 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.23it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.56it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.21it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.79it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.30it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.85it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.64it/s]
100%|██████████| 8/8 [00:01<00:00,  5.46it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63542 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63355 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.61it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.29it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.09it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.23it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.62it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.20it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.28it/s]
100%|██████████| 8/8 [00:01<00:00,  5.34it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63632 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63633 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.40it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.61it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.06it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.87it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.08it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.37it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63632 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63633 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.50it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.13it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.98it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.22it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.63it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.32it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.50it/s]
100%|██████████| 8/8 [00:01<00:00,  5.90it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63674 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63675 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.72it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.37it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.43it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.43it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.54it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.71it/s]
100%|██████████| 8/8 [00:01<00:00,  5.86it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63769 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63770 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.03it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.93it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.74it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.36it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.84it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.52it/s]
100%|██████████| 8/8 [00:01<00:00,  6.43it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63948 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63949 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.09it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.61it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.64it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.01it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.39it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.65it/s]
100%|██████████| 8/8 [00:01<00:00,  6.21it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:63949 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:63948 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:63989 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:63990 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:63989 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:63990 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.38it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.91it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.63it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.87it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.07it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.25it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.52it/s]
100%|██████████| 8/8 [00:01<00:00,  5.77it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64004 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64005 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.23it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.50it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.55it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.32it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.63it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.64it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.11it/s]
                                             
获取成交额排行数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
成功获取3个指数数据
INFO:     127.0.0.1:64078 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64079 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64141 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64142 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64145 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.19it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.82it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.23it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.84it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.21it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.74it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.10it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64141 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64146 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64148 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.29it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.13it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.40it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.64it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.91it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.29it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.76it/s]
100%|██████████| 8/8 [00:01<00:00,  6.59it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64146 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64148 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.14it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.57it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.43it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.81it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.89it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  7.00it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.17it/s]
100%|██████████| 8/8 [00:01<00:00,  6.54it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64196 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64197 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.79it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.95it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.69it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.46it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.82it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.07it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.03it/s]
100%|██████████| 8/8 [00:02<00:00,  2.68it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64284 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64286 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64287 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:64288 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.17it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.57it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.56it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.77it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.00it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.58it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.60it/s]
100%|██████████| 8/8 [00:01<00:00,  6.02it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64359 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64360 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.07it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.25it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.16it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.83it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.23it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.86it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.30it/s]
100%|██████████| 8/8 [00:01<00:00,  6.12it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64360 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64359 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.26it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.85it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.54it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.27it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.71it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.14it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.12it/s]
100%|██████████| 8/8 [00:01<00:00,  5.91it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64412 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64413 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.66it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.92it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.20it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.25it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.38it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.54it/s]
100%|██████████| 8/8 [00:01<00:00,  5.28it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64482 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64483 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.22it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.78it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.21it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.76it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.52it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.99it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.02it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64565 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64566 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.10it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.59it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.99it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.36it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.54it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.48it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.65it/s]
100%|██████████| 8/8 [00:01<00:00,  7.38it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64566 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64565 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.46it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.59it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.22it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.71it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.10it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.60it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.98it/s]
100%|██████████| 8/8 [00:01<00:00,  6.00it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64608 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64609 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.68it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.42it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.49it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.34it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.05it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.04it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.19it/s]
100%|██████████| 8/8 [00:01<00:00,  5.35it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64693 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64694 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.34it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.63it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.64it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.81it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.20it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.70it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.07it/s]
100%|██████████| 8/8 [00:01<00:00,  6.79it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64781 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64782 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.22it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.93it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.16it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.24it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  7.12it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.98it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.96it/s]
100%|██████████| 8/8 [00:01<00:00,  7.65it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64827 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64781 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64855 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64856 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.94it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.84it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.37it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.56it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.39it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.68it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.77it/s]
100%|██████████| 8/8 [00:02<00:00,  1.96it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64855 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.89it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.78it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.34it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.55it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.09it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.27it/s]
100%|██████████| 8/8 [00:01<00:00,  6.91it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64858 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:64937 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:64937 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.19it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.63it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.72it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.43it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.46it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.90it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.16it/s]
100%|██████████| 8/8 [00:01<00:00,  6.91it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65124 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65125 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:65148 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:65149 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:65158 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:65159 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.79it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.28it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.44it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.15it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.99it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.90it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.43it/s]
100%|██████████| 8/8 [00:01<00:00,  5.67it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65124 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65125 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:65148 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:65159 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.03it/s]
 25%|██▌       | 2/8 [00:00<00:02,  2.85it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.89it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.73it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.34it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.90it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.22it/s]
100%|██████████| 8/8 [00:01<00:00,  7.01it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:64970 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65175 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:65209 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:65210 - "GET /api/market/commodities HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.67it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.24it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.90it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.53it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.99it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.11it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.05it/s]
100%|██████████| 8/8 [00:01<00:00,  5.54it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65236 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65237 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.73it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.89it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.98it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.02it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.41it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.93it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.34it/s]
100%|██████████| 8/8 [00:01<00:00,  6.45it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65306 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65307 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.94it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.89it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.79it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.95it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.86it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.40it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.67it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65306 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.28it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.83it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.94it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.27it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.45it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.58it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.37it/s]
100%|██████████| 8/8 [00:01<00:00,  6.58it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65346 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65307 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:65348 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.38it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.92it/s]
 38%|███▊      | 3/8 [00:01<00:03,  1.58it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.10it/s]
 62%|██████▎   | 5/8 [00:01<00:01,  2.78it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  3.18it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.49it/s]
100%|██████████| 8/8 [00:02<00:00,  4.34it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65423 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65424 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:65425 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:65426 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:65424 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:65425 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.89it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.91it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.31it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.14it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.48it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.83it/s]
100%|██████████| 8/8 [00:01<00:00,  5.68it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65505 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65506 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.35it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.88it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.14it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.44it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.74it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.54it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.31it/s]
100%|██████████| 8/8 [00:01<00:00,  6.08it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:65506 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:65505 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.99it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.59it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.00it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.62it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.03it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.24it/s]
100%|██████████| 8/8 [00:01<00:00,  6.26it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49174 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49175 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.04it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.81it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.59it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.96it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.31it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.48it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.56it/s]
                                             
获取外汇数据失败: IncompleteRead(786139 bytes read, 309436 more expected)
成功获取3个指数数据
INFO:     127.0.0.1:49174 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49175 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.67it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.80it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.33it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.15it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.59it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.36it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.64it/s]
                                             
获取外汇数据失败: IncompleteRead(769755 bytes read, 325820 more expected)
成功获取3个指数数据
INFO:     127.0.0.1:49249 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.15it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.53it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.10it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.08it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.65it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.02it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.28it/s]
100%|██████████| 8/8 [00:01<00:00,  6.14it/s]
                                             
获取成交额排行数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
成功获取3个指数数据
INFO:     127.0.0.1:49174 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.38it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.67it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.52it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.94it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.24it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.85it/s]
100%|██████████| 8/8 [00:01<00:00,  6.73it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49295 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49249 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.07it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.44it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.61it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.34it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.82it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.36it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.77it/s]
100%|██████████| 8/8 [00:01<00:00,  6.50it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49295 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49249 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49382 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:49295 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:49421 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49382 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49249 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49503 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49421 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49295 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49504 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49249 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:49503 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:49382 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:49539 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49295 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49504 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49421 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49540 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49503 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49249 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49382 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49539 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50068 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49504 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49421 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49540 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49295 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50070 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49539 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49503 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49540 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:49539 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49503 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:49504 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:49421 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49298 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:49539 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.18it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.71it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.74it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.98it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.00it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.97it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.44it/s]
100%|██████████| 8/8 [00:01<00:00,  5.77it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49540 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:49295 - "OPTIONS /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "OPTIONS /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:08,  1.17s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.74it/s]
 38%|███▊      | 3/8 [00:01<00:01,  2.57it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.10it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  3.79it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  4.07it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  4.70it/s]
100%|██████████| 8/8 [00:02<00:00,  5.02it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50167 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:49381 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.16it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.29it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.73it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.05it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.24it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.47it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.57it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:49295 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50168 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.66it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.08it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.94it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.58it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.50it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.30it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.34it/s]
100%|██████████| 8/8 [00:01<00:00,  5.96it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50167 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.09it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.71it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.76it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.08it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.43it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.46it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.62it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50170 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50168 - "OPTIONS /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50200 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.44it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.85it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.93it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.96it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.79it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.63it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.50it/s]
100%|██████████| 8/8 [00:01<00:00,  7.19it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50212 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50168 - "GET /api/cache/indices HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50218 - "OPTIONS /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50219 - "OPTIONS /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:50220 - "OPTIONS /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50225 - "OPTIONS /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50226 - "OPTIONS /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:50227 - "OPTIONS /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50237 - "OPTIONS /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:50238 - "OPTIONS /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50168 - "GET /api/cache/hot-stocks HTTP/1.1" 404 Not Found

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.70it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.18it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.27it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.01it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.52it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.97it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.13it/s]
100%|██████████| 8/8 [00:02<00:00,  2.15it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50237 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50284 - "GET /api/cache/market-stats HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50168 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:50218 - "GET /api/cache/indices HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50219 - "GET /api/cache/hot-stocks HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50220 - "GET /api/cache/market-stats HTTP/1.1" 404 Not Found

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.12it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.33it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.71it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.80it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  7.00it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.94it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  7.04it/s]
100%|██████████| 8/8 [00:01<00:00,  7.66it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50237 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50220 - "GET /api/cache/indices HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50219 - "GET /api/cache/hot-stocks HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50218 - "GET /api/cache/market-stats HTTP/1.1" 404 Not Found

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.34it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.98it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.04it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.53it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.12it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.59it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.01it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50313 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:50237 - "OPTIONS /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50314 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.10it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.44it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.52it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.74it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.67it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.75it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.74it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50313 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.86it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.29it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.73it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.79it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.86it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  7.07it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.82it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50316 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50314 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:50237 - "GET /api/cache/sectors HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50347 - "OPTIONS /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50349 - "OPTIONS /api/cache/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.22it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.53it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.62it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.37it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.41it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.81it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.02it/s]
100%|██████████| 8/8 [00:02<00:00,  2.60it/s]
                                             
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
成功获取3个指数数据
INFO:     127.0.0.1:50316 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50354 - "GET /api/cache/commodities HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50237 - "GET /api/cache/forex HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50349 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50314 - "OPTIONS /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50347 - "GET /api/cache/market-stats HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70826]
INFO:     Started server process [74878]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:50415 - "OPTIONS /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:50427 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50430 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50415 - "OPTIONS /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50427 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50431 - "OPTIONS /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50430 - "OPTIONS /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:50456 - "OPTIONS /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50458 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50460 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50431 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:50415 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50456 - "GET /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50458 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50430 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50464 - "GET /api/market/forex HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.01it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.98it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.61it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.81it/s]
 62%|██████▎   | 5/8 [00:01<00:01,  2.31it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  2.77it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.24it/s]
100%|██████████| 8/8 [00:02<00:00,  2.65it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50460 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50465 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50466 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50543 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:50546 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:50549 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:50550 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50551 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50550 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.32it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.54it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.05it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.22it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.09it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.92it/s]
100%|██████████| 8/8 [00:01<00:00,  5.46it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50554 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50551 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:50549 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50546 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50466 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50465 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50554 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50551 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50546 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.80it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.43it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.99it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.84it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.87it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.14it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.33it/s]
100%|██████████| 8/8 [00:01<00:00,  4.95it/s]
                                             
WARNING:  WatchFiles detected changes in 'services/market_service.py'. Reloading...
成功获取3个指数数据
INFO:     127.0.0.1:50465 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50466 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50554 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.66it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.94it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.93it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.02it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  7.16it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.91it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.88it/s]
100%|██████████| 8/8 [00:01<00:00,  7.55it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:50549 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [74878]
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/Users/<USER>/miniforge3/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniforge3/lib/python3.12/multiprocessing/spawn.py", line 131, in _main
    prepare(preparation_data)
  File "/Users/<USER>/miniforge3/lib/python3.12/multiprocessing/spawn.py", line 246, in prepare
    _fixup_main_from_path(data['init_main_from_path'])
  File "/Users/<USER>/miniforge3/lib/python3.12/multiprocessing/spawn.py", line 297, in _fixup_main_from_path
    main_content = runpy.run_path(main_path,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen runpy>", line 287, in run_path
  File "<frozen runpy>", line 98, in _run_module_code
  File "<frozen runpy>", line 88, in _run_code
  File "/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py", line 13, in <module>
    from services.market_service import MarketService
  File "/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/services/market_service.py", line 588
    return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
SyntaxError: expected 'except' or 'finally' block
WARNING:  WatchFiles detected changes in 'services/market_service.py'. Reloading...
INFO:     Started server process [75124]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:50701 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.32it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.40it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.42it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.29it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.57it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.93it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.33it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:50703 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50701 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50731 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50733 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50737 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50738 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50815 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50737 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50816 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50738 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:08,  1.22s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.66it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.25it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.89it/s]
 62%|██████▎   | 5/8 [00:02<00:00,  3.13it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  3.68it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.82it/s]
100%|██████████| 8/8 [00:02<00:00,  4.19it/s]
                                             
WARNING:  WatchFiles detected changes in 'services/market_service.py'. Reloading...
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:50737 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.78it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.73it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.34it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.28it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.70it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.42it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.86it/s]
100%|██████████| 8/8 [00:01<00:00,  6.66it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:50816 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:50815 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50738 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75124]
INFO:     Started server process [75206]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:50864 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:50866 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50864 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51014 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:50866 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.97it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.99it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.61it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.74it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.32it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.69it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.14it/s]
100%|██████████| 8/8 [00:01<00:00,  6.73it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51016 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51014 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.37it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.24it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.75it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.21it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.57it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.91it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.18it/s]
100%|██████████| 8/8 [00:01<00:00,  6.85it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51018 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51020 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  3.64it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.49it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.20it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.08it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.70it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.43it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.53it/s]WARNING:  WatchFiles detected changes in 'services/market_service.py'. Reloading...

100%|██████████| 8/8 [00:01<00:00,  6.04it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51018 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51020 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.05it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.31it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.32it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.38it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  7.32it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  7.25it/s]
 88%|████████▊ | 7/8 [00:00<00:00,  7.11it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51078 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75206]
INFO:     Started server process [75371]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:51105 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51107 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51110 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51111 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51107 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51105 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51175 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.68it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.40it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.62it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.06it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.87it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.38it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.69it/s]
100%|██████████| 8/8 [00:01<00:00,  5.33it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51110 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51177 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:03,  2.21it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.52it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.07it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.81it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.44it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.65it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.66it/s]
100%|██████████| 8/8 [00:01<00:00,  5.53it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51189 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51177 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51190 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.94it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.83it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.72it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.39it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.48it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.98it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.36it/s]
100%|██████████| 8/8 [00:01<00:00,  6.14it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51189 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51190 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51246 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51177 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51253 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51246 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.87it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.44it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.55it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.97it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.46it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.03it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.50it/s]
100%|██████████| 8/8 [00:01<00:00,  5.76it/s]
                                             
WARNING:  WatchFiles detected changes in 'services/market_service.py'. Reloading...
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51177 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51255 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51253 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.81it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.97it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.65it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.37it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.68it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.32it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.12it/s]
100%|██████████| 8/8 [00:01<00:00,  6.00it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51190 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51189 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75371]
INFO:     Started server process [75467]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Redis连接成功
Redis连接成功
Redis连接成功
AI量化交易系统后端服务启动成功！
API文档地址: http://localhost:8000/docs
开始缓存预热...
缓存预热完成
INFO:     127.0.0.1:51377 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51385 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51386 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:03,  2.26it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.52it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.55it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.01it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.94it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.33it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.01it/s]
100%|██████████| 8/8 [00:01<00:00,  5.46it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51385 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.92it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.37it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.66it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.52it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.93it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.13it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.31it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51388 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51386 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51390 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51494 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51495 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.28it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.22it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.88it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.58it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.26it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.66it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.38it/s]
100%|██████████| 8/8 [00:01<00:00,  5.53it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51495 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51494 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51512 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51513 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51512 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51513 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.30it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.11it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.68it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.71it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.60it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.54it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.63it/s]
100%|██████████| 8/8 [00:01<00:00,  7.40it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51561 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51513 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.30it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.13it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.34it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.62it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.61it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.11it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.72it/s]
100%|██████████| 8/8 [00:01<00:00,  4.87it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51513 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51561 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51630 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51631 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.63it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.75it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.92it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.92it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.93it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.88it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.82it/s]
100%|██████████| 8/8 [00:01<00:00,  5.14it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51631 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51630 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51631 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51630 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:51682 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.48it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.45it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.82it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.13it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.16it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.34it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51682 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51630 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.61it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.09it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.35it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.52it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.46it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.56it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.01it/s]
100%|██████████| 8/8 [00:01<00:00,  6.20it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51631 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51735 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51736 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:51735 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51737 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51736 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:51735 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51631 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51753 - "GET /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:51754 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:51754 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:51753 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:51631 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
外汇API调用失败: 'date'
INFO:     127.0.0.1:51631 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:51756 - "GET /api/market/commodities HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.32it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.73it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.70it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.23it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.23it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.26it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.38it/s]
100%|██████████| 8/8 [00:01<00:00,  7.17it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51805 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51631 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51756 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:51758 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51806 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.06it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.17it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.85it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.34it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.54it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.58it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.68it/s]
100%|██████████| 8/8 [00:01<00:00,  7.17it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51805 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.72it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.63it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.75it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.76it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.43it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.74it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.11it/s]
100%|██████████| 8/8 [00:01<00:00,  6.85it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51807 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51806 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:51631 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.22it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.36it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.42it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.62it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.96it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.04it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.89it/s]
100%|██████████| 8/8 [00:01<00:00,  5.09it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51758 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.09it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.72it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.57it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.49it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.01it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.86it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.56it/s]
100%|██████████| 8/8 [00:01<00:00,  6.45it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51756 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:07,  1.14s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.82it/s]
 38%|███▊      | 3/8 [00:01<00:01,  2.78it/s]
 50%|█████     | 4/8 [00:01<00:01,  3.56it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.35it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.01it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  5.01it/s]
100%|██████████| 8/8 [00:02<00:00,  5.51it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51869 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:51871 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:51891 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51892 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:51893 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:51924 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51925 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:51929 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:08,  1.24s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.52it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.21it/s]
 50%|█████     | 4/8 [00:02<00:02,  1.85it/s]
 62%|██████▎   | 5/8 [00:02<00:01,  2.35it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  2.88it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.56it/s]
100%|██████████| 8/8 [00:03<00:00,  4.43it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:51925 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:51924 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.71it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.51it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.66it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.39it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.98it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.50it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.73it/s]
100%|██████████| 8/8 [00:01<00:00,  5.83it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52028 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.68it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.54it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.00it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.45it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.80it/s]
 75%|███████▌  | 6/8 [00:02<00:01,  1.82it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.39it/s]
100%|██████████| 8/8 [00:02<00:00,  3.14it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:51929 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.43it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.46it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.96it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.43it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.85it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.19it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.00it/s]
100%|██████████| 8/8 [00:02<00:00,  2.67it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52029 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52030 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52036 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52037 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52038 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52049 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52050 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.93it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.57it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.80it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.84it/s]
 62%|██████▎   | 5/8 [00:01<00:01,  1.89it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  2.34it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.99it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52049 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52056 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52050 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52140 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52142 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52143 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.62it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.17it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.90it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.14it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.24it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.55it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.63it/s]
100%|██████████| 8/8 [00:02<00:00,  2.12it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52171 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52172 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52189 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52190 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52196 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.73it/s]
 25%|██▌       | 2/8 [00:01<00:04,  1.27it/s]
 38%|███▊      | 3/8 [00:01<00:02,  1.91it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.69it/s]
 62%|██████▎   | 5/8 [00:02<00:01,  2.49it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  2.93it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.33it/s]
100%|██████████| 8/8 [00:02<00:00,  3.80it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52190 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52189 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.04it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.19it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.70it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.60it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.50it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.55it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.73it/s]
100%|██████████| 8/8 [00:01<00:00,  7.25it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52196 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52246 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52190 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52247 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52248 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52316 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52318 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52319 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52330 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:01<00:08,  1.19s/it]
 25%|██▌       | 2/8 [00:01<00:03,  1.67it/s]
 38%|███▊      | 3/8 [00:01<00:02,  2.45it/s]
 50%|█████     | 4/8 [00:02<00:02,  1.36it/s]
 62%|██████▎   | 5/8 [00:02<00:01,  1.87it/s]
 75%|███████▌  | 6/8 [00:03<00:00,  2.44it/s]
 88%|████████▊ | 7/8 [00:03<00:00,  2.93it/s]
100%|██████████| 8/8 [00:03<00:00,  3.41it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52330 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52190 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52196 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52350 - "GET /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:52351 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:52352 - "OPTIONS /api/market/risk-indicators HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.03it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.70it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.94it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.88it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.41it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.46it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.52it/s]
100%|██████████| 8/8 [00:01<00:00,  7.26it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52403 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52351 - "GET /api/market/risk-indicators HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52352 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52350 - "GET /api/market/commodities HTTP/1.1" 200 OK
外汇API调用失败: 'date'
INFO:     127.0.0.1:52190 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:52196 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:52330 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52404 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.24it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.30it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.02it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.06it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.55it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.28it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.18it/s]
100%|██████████| 8/8 [00:01<00:00,  5.55it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52403 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.50it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.08it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.62it/s]
 50%|█████     | 4/8 [00:01<00:02,  1.63it/s]
 62%|██████▎   | 5/8 [00:01<00:01,  2.22it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  2.87it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  3.55it/s]
100%|██████████| 8/8 [00:02<00:00,  4.37it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52405 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52404 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52410 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52411 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52424 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52404 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52425 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52426 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52464 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52501 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52464 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52502 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.59it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.32it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.35it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.61it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.92it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.38it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.57it/s]
100%|██████████| 8/8 [00:01<00:00,  7.25it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52501 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52503 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52502 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.25it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.53it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.65it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.14it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.85it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.94it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.39it/s]
100%|██████████| 8/8 [00:01<00:00,  6.25it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52464 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52404 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52518 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52657 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.23it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.04it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.42it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.61it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.41it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.55it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.49it/s]
100%|██████████| 8/8 [00:01<00:00,  7.11it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52659 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52657 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52661 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52663 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52657 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.40it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.47it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.00it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.89it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.23it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.65it/s]
100%|██████████| 8/8 [00:01<00:00,  6.55it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52666 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.09it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.14it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.29it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.23it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.79it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.64it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.67it/s]
100%|██████████| 8/8 [00:01<00:00,  5.95it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52663 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.80it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.52it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.73it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.67it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.92it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.98it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  7.07it/s]
100%|██████████| 8/8 [00:01<00:00,  7.58it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52667 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52676 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52677 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.00it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.93it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.90it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.94it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.85it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.89it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.89it/s]
100%|██████████| 8/8 [00:01<00:00,  5.23it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52703 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52677 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.12it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.69it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.14it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.52it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.63it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.72it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.78it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52676 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52725 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52726 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52725 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52763 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.08it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.51it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.00it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.72it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.84it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.08it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.33it/s]
100%|██████████| 8/8 [00:01<00:00,  6.48it/s]
                                             
外汇API调用失败: 'date'
板块API调用失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
成功获取3个指数数据
INFO:     127.0.0.1:52810 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.41it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.40it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.18it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.72it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.18it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.24it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.09it/s]
100%|██████████| 8/8 [00:01<00:00,  5.94it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:52725 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52676 - "GET /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:52881 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.82it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.53it/s]
 38%|███▊      | 3/8 [00:01<00:03,  1.65it/s]
 50%|█████     | 4/8 [00:01<00:01,  2.33it/s]
 62%|██████▎   | 5/8 [00:01<00:01,  2.98it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  3.67it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  4.29it/s]
100%|██████████| 8/8 [00:02<00:00,  5.23it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52810 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52882 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.89it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.35it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.85it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.40it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.44it/s]
 75%|███████▌  | 6/8 [00:01<00:01,  1.96it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.40it/s]
100%|██████████| 8/8 [00:02<00:00,  3.14it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52676 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52881 - "GET /api/market/commodities HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.75it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.27it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.86it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.57it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.85it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.02it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.36it/s]
100%|██████████| 8/8 [00:01<00:00,  6.39it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:52725 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52901 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:52810 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:52882 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52902 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:52914 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52882 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52925 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:52927 - "GET /api/market/risk-indicators HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52935 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:52989 - "GET /api/market/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.99it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.89it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.90it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.23it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.43it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.76it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.21it/s]
100%|██████████| 8/8 [00:01<00:00,  5.87it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53011 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:52989 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:52927 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:52882 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:52925 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53012 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:53051 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53052 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:53051 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53053 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53052 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:53101 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53053 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.82it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.94it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.78it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.98it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.50it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.31it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.75it/s]
100%|██████████| 8/8 [00:01<00:00,  6.63it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:53051 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53105 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53111 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.16it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.72it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.80it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.76it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.86it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  2.06it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.62it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:53105 - "GET /api/market/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.97it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.89it/s]
 38%|███▊      | 3/8 [00:00<00:01,  3.06it/s]
 50%|█████     | 4/8 [00:00<00:01,  3.96it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  4.70it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.37it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.83it/s]
100%|██████████| 8/8 [00:01<00:00,  6.20it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:53051 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53112 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53111 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:00,  7.48it/s]
 25%|██▌       | 2/8 [00:00<00:00,  7.27it/s]
 38%|███▊      | 3/8 [00:00<00:00,  7.26it/s]
 50%|█████     | 4/8 [00:00<00:00,  7.26it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.68it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.08it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.71it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53053 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53151 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.74it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.58it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.06it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.86it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.20it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.07it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.18it/s]
100%|██████████| 8/8 [00:01<00:00,  6.71it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53112 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
获取成交额排行数据失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
INFO:     127.0.0.1:53105 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.21it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.49it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.81it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.20it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.31it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.71it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.98it/s]
100%|██████████| 8/8 [00:01<00:00,  6.19it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53154 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53187 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.83it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.23it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.38it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.20it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.48it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.59it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.92it/s]
100%|██████████| 8/8 [00:01<00:00,  5.86it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53189 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53199 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53207 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:53208 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53230 - "GET /api/cache/indices HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.32it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.43it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.50it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.76it/s]
 62%|██████▎   | 5/8 [00:01<00:01,  1.90it/s]
 75%|███████▌  | 6/8 [00:02<00:00,  2.41it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.79it/s]
100%|██████████| 8/8 [00:02<00:00,  3.59it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:53207 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53199 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:53208 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.30it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.67it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.09it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.50it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.51it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.53it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.62it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:53230 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53232 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK
INFO:     127.0.0.1:53308 - "GET /api/cache/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53310 - "GET /api/cache/hot-stocks HTTP/1.1" 200 OK
INFO:     127.0.0.1:53311 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.80it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.41it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.38it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.61it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.49it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.46it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.60it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53231 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53331 - "GET /api/cache/sectors HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.13it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.50it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.30it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.36it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.47it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  4.35it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  4.60it/s]
100%|██████████| 8/8 [00:01<00:00,  5.55it/s]
                                             
成功获取3个指数数据
INFO:     127.0.0.1:53311 - "GET /api/market/indices HTTP/1.1" 200 OK
INFO:     127.0.0.1:53310 - "GET /api/market/hot-stocks?stock_type=gainers HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.48it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.42it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.72it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.79it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.79it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.72it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.69it/s]
100%|██████████| 8/8 [00:01<00:00,  7.15it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53341 - "GET /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.49it/s]
 25%|██▌       | 2/8 [00:00<00:00,  6.83it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.86it/s]
 50%|█████     | 4/8 [00:00<00:00,  6.84it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.77it/s]
 75%|███████▌  | 6/8 [00:00<00:00,  6.86it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.78it/s]
100%|██████████| 8/8 [00:01<00:00,  7.53it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53231 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53331 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53407 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.75it/s]
 25%|██▌       | 2/8 [00:00<00:01,  4.25it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.93it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.52it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.09it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.55it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.01it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53341 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53408 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53331 - "GET /api/cache/market-stats HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  6.41it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.57it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.38it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.95it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  6.30it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  6.09it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  6.09it/s]
100%|██████████| 8/8 [00:01<00:00,  6.37it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53231 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53407 - "GET /api/cache/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:53408 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53486 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53510 - "GET /api/cache/commodities HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.58it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.81it/s]
 38%|███▊      | 3/8 [00:00<00:00,  6.46it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.80it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  5.69it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.54it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.46it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53486 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53407 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53408 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53511 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:53510 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "OPTIONS /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:53512 - "GET /api/market/risk-indicators HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53486 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:53407 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53408 - "OPTIONS /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53553 - "GET /api/cache/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "GET /api/market/commodities HTTP/1.1" 200 OK
INFO:     127.0.0.1:53554 - "OPTIONS /api/market/risk-indicators HTTP/1.1" 200 OK
INFO:     127.0.0.1:53408 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53568 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "GET /api/market/risk-indicators HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53554 - "OPTIONS /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:53606 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53408 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53608 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "GET /api/market/forex HTTP/1.1" 200 OK
INFO:     127.0.0.1:53608 - "GET /api/market/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53231 - "OPTIONS /api/dashboard/overview HTTP/1.1" 200 OK

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  5.48it/s]
 25%|██▌       | 2/8 [00:00<00:01,  3.09it/s]
 38%|███▊      | 3/8 [00:00<00:01,  4.12it/s]
 50%|█████     | 4/8 [00:00<00:00,  4.94it/s]
 62%|██████▎   | 5/8 [00:01<00:00,  5.40it/s]
 75%|███████▌  | 6/8 [00:02<00:01,  1.67it/s]
 88%|████████▊ | 7/8 [00:02<00:00,  2.21it/s]
100%|██████████| 8/8 [00:02<00:00,  2.93it/s]
                                             
外汇API调用失败: 'date'
成功获取3个指数数据
INFO:     127.0.0.1:53408 - "GET /api/dashboard/overview HTTP/1.1" 200 OK
INFO:     127.0.0.1:53608 - "GET /api/market/risk-indicators HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53606 - "GET /api/cache/market-stats HTTP/1.1" 200 OK
INFO:     127.0.0.1:53486 - "GET /api/cache/sectors HTTP/1.1" 200 OK
INFO:     127.0.0.1:53688 - "GET /api/market/risk-indicators HTTP/1.1" 404 Not Found

  0%|          | 0/8 [00:00<?, ?it/s]
 12%|█▎        | 1/8 [00:00<00:01,  4.33it/s]
 25%|██▌       | 2/8 [00:00<00:01,  5.47it/s]
 38%|███▊      | 3/8 [00:00<00:00,  5.96it/s]
 50%|█████     | 4/8 [00:00<00:00,  5.42it/s]
 62%|██████▎   | 5/8 [00:00<00:00,  4.82it/s]
 75%|███████▌  | 6/8 [00:01<00:00,  5.12it/s]
 88%|████████▊ | 7/8 [00:01<00:00,  5.57it/s]
100%|██████████| 8/8 [00:01<00:00,  6.24it/s]
                                             
/Users/<USER>/miniforge3/lib/python3.12/multiprocessing/resource_tracker.py:254: UserWarning: resource_tracker: There appear to be 7 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
