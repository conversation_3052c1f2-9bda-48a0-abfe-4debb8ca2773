#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新浪接口优先策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import akshare as ak
import pandas as pd
from services.market_service import MarketService

async def test_sina_priority():
    """测试新浪接口优先策略"""
    print("🧪 测试新浪接口优先策略")
    print("=" * 50)
    
    market_service = MarketService()
    
    # 测试1: 直接测试新浪接口
    print("\n1️⃣ 测试新浪股票列表接口")
    try:
        sina_df = ak.stock_zh_a_spot()
        print(f"✅ 新浪接口成功，获取到 {len(sina_df)} 条股票数据")
        print(f"   前3只股票: {sina_df.head(3)['名称'].tolist()}")
    except Exception as e:
        print(f"❌ 新浪接口失败: {e}")
    
    # 测试2: 直接测试东财接口
    print("\n2️⃣ 测试东财股票列表接口")
    try:
        em_df = ak.stock_zh_a_spot_em()
        print(f"✅ 东财接口成功，获取到 {len(em_df)} 条股票数据")
        print(f"   前3只股票: {em_df.head(3)['名称'].tolist()}")
    except Exception as e:
        print(f"❌ 东财接口失败: {e}")
    
    # 测试3: 测试降级策略
    print("\n3️⃣ 测试MarketService降级策略")
    try:
        # 测试_fetch_with_fallback方法
        def test_sina():
            return ak.stock_zh_a_spot()
        
        def test_em():
            return ak.stock_zh_a_spot_em()
        
        result_df = market_service._fetch_with_fallback(test_sina, test_em, "股票列表测试")
        print(f"✅ 降级策略成功，获取到 {len(result_df)} 条数据")
        print(f"   前3只股票: {result_df.head(3)['名称'].tolist()}")
    except Exception as e:
        print(f"❌ 降级策略失败: {e}")
    
    # 测试4: 测试指数数据
    print("\n4️⃣ 测试指数数据获取")
    try:
        indices_data = await market_service.get_indices_data()
        print(f"✅ 指数数据获取成功，共 {len(indices_data)} 个指数")
        for idx in indices_data:
            print(f"   {idx['name']}: {idx['current']} ({idx['change_percent']:+.2f}%)")
    except Exception as e:
        print(f"❌ 指数数据获取失败: {e}")
    
    # 测试5: 测试股票列表API
    print("\n5️⃣ 测试股票列表API")
    try:
        stock_list = await market_service.get_stock_list(page=1, size=5)
        print(f"✅ 股票列表API成功，共 {stock_list['total']} 只股票")
        print("   前5只股票:")
        for stock in stock_list['list'][:5]:
            print(f"   {stock['name']} ({stock['code']}): ¥{stock['price']} ({stock['change_percent']:+.2f}%)")
    except Exception as e:
        print(f"❌ 股票列表API失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")

if __name__ == "__main__":
    asyncio.run(test_sina_priority()) 