#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存预热服务 - 异步预热所有数据，提升接口响应速度
"""

import asyncio
import logging
from typing import Dict, Any
from services.market_service import MarketService
from services.cache_service import cache_service

logger = logging.getLogger(__name__)

class CacheWarmupService:
    def __init__(self):
        self.market_service = MarketService()
        self.warmup_tasks = []
        
    async def warmup_all_data(self):
        """预热所有数据"""
        logger.info("🔥 开始缓存预热...")
        
        # 定义预热任务
        warmup_tasks = [
            ("指数数据", self._warmup_indices_data),
            ("板块数据", self._warmup_sectors_data),
            ("热门股票", self._warmup_hot_stocks),
            ("商品期货", self._warmup_commodities_data),
            ("外汇数据", self._warmup_forex_data),
            ("市场统计", self._warmup_market_statistics),
            ("风险指标", self._warmup_risk_indicators),
            ("全球指数", self._warmup_global_indices),
        ]
        
        # 并发执行预热任务
        results = await asyncio.gather(*[
            self._safe_warmup(name, task) for name, task in warmup_tasks
        ], return_exceptions=True)
        
        # 统计预热结果
        success_count = sum(1 for result in results if result is True)
        total_count = len(warmup_tasks)
        
        logger.info(f"🎉 缓存预热完成: {success_count}/{total_count} 个数据源预热成功")
        
        return {
            "success_count": success_count,
            "total_count": total_count,
            "success_rate": success_count / total_count if total_count > 0 else 0
        }
    
    async def _safe_warmup(self, name: str, task) -> bool:
        """安全执行预热任务"""
        try:
            await task()
            logger.info(f"✅ {name}预热成功")
            return True
        except Exception as e:
            logger.warning(f"⚠️ {name}预热失败: {e}")
            return False
    
    async def _warmup_indices_data(self):
        """预热指数数据"""
        await self.market_service.get_indices_data()
    
    async def _warmup_sectors_data(self):
        """预热板块数据"""
        await self.market_service.get_sectors_data()
    
    async def _warmup_hot_stocks(self):
        """预热热门股票数据"""
        await self.market_service.get_hot_stocks()
    
    async def _warmup_commodities_data(self):
        """预热商品期货数据"""
        await self.market_service.get_commodities_data()
    
    async def _warmup_forex_data(self):
        """预热外汇数据"""
        await self.market_service.get_forex_data()
    
    async def _warmup_market_statistics(self):
        """预热市场统计数据"""
        await self.market_service.get_market_statistics()
    
    async def _warmup_risk_indicators(self):
        """预热风险指标数据"""
        await self.market_service.get_risk_indicators()
    
    async def _warmup_global_indices(self):
        """预热全球指数数据"""
        await self.market_service.get_global_indices()
    
    async def get_cached_dashboard_data(self) -> Dict[str, Any]:
        """获取缓存的监控大屏数据（快速返回）"""
        try:
            # 并发获取所有缓存数据
            results = await asyncio.gather(
                cache_service.get("global_indices"),
                cache_service.get("hot_stocks"),
                cache_service.get("commodities_data"),
                cache_service.get("forex_data"),
                cache_service.get("sectors_data"),
                cache_service.get("market_statistics"),
                cache_service.get("risk_indicators"),
                return_exceptions=True
            )
            
            global_indices, hot_stocks, commodities, forex, sectors, market_stats, risk_indicators = results
            
            # 如果任何数据为空，触发异步预热
            missing_data = []
            if not global_indices or isinstance(global_indices, Exception):
                missing_data.append("全球指数")
                global_indices = {"亚洲": [], "欧洲": [], "美洲": []}
            
            if not hot_stocks or isinstance(hot_stocks, Exception):
                missing_data.append("热门股票")
                hot_stocks = []
            
            if not commodities or isinstance(commodities, Exception):
                missing_data.append("商品期货")
                commodities = []
            
            if not forex or isinstance(forex, Exception):
                missing_data.append("外汇数据")
                forex = []
            
            if not sectors or isinstance(sectors, Exception):
                missing_data.append("板块数据")
                sectors = []
            
            if not market_stats or isinstance(market_stats, Exception):
                missing_data.append("市场统计")
                market_stats = {}
            
            if not risk_indicators or isinstance(risk_indicators, Exception):
                missing_data.append("风险指标")
                risk_indicators = {}
            
            if missing_data:
                logger.warning(f"缓存数据缺失: {', '.join(missing_data)}，触发后台预热")
                # 异步触发预热，不等待结果
                asyncio.create_task(self.warmup_all_data())
            
            return {
                "global_indices": global_indices,
                "hot_stocks": hot_stocks,
                "commodities": commodities,
                "forex": forex,
                "sectors": sectors,
                "market_stats": market_stats,
                "risk_indicators": risk_indicators,
                "cached": True,
                "missing_data": missing_data
            }
            
        except Exception as e:
            logger.error(f"获取缓存数据失败: {e}")
            # 返回空数据，触发预热
            asyncio.create_task(self.warmup_all_data())
            return {
                "global_indices": {"亚洲": [], "欧洲": [], "美洲": []},
                "hot_stocks": [],
                "commodities": [],
                "forex": [],
                "sectors": [],
                "market_stats": {},
                "risk_indicators": {},
                "cached": False,
                "error": str(e)
            }

# 创建全局实例
cache_warmup_service = CacheWarmupService() 