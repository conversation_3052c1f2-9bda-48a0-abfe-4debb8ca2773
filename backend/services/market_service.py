import akshare as ak
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import asyncio
from services.cache_service import cache_service
from config.settings import get_settings
import logging

settings = get_settings()
logger = logging.getLogger(__name__)

class MarketService:
    """行情数据服务"""
    
    def __init__(self):
        self.cache = cache_service
        
    async def get_stock_list(self, page: int = 1, size: int = 50, 
                           sort_by: str = "code", sort_order: str = "asc", 
                           search: Optional[str] = None) -> Dict[str, Any]:
        """获取股票列表"""
        cache_key = f"stock_list:{page}:{size}:{sort_by}:{sort_order}:{search or 'all'}"
        
        async def fetch_data():
            try:
                # 获取A股实时行情
                df = ak.stock_zh_a_spot_em()
                
                # 数据清洗和转换
                stocks = []
                for _, row in df.iterrows():
                    stock = {
                        "code": row.get("代码", ""),
                        "name": row.get("名称", ""),
                        "price": float(row.get("最新价", 0)),
                        "change": float(row.get("涨跌额", 0)),
                        "change_percent": float(row.get("涨跌幅", 0)),
                        "volume": row.get("成交量", "0"),
                        "turnover": row.get("成交额", "0"),
                        "high": float(row.get("最高", 0)),
                        "low": float(row.get("最低", 0)),
                        "open": float(row.get("今开", 0)),
                        "market_cap": row.get("总市值", "0"),
                        "pe": float(row.get("市盈率-动态", 0)) if row.get("市盈率-动态") else None,
                        "pb": float(row.get("市净率", 0)) if row.get("市净率") else None
                    }
                    stocks.append(stock)
                
                # 搜索过滤
                if search:
                    stocks = [s for s in stocks if search.upper() in s["code"] or search in s["name"]]
                
                # 排序
                reverse = sort_order == "desc"
                if sort_by in ["price", "change", "change_percent", "high", "low", "open"]:
                    stocks.sort(key=lambda x: x.get(sort_by, 0), reverse=reverse)
                else:
                    stocks.sort(key=lambda x: x.get(sort_by, ""), reverse=reverse)
                
                # 分页
                start = (page - 1) * size
                end = start + size
                paginated_stocks = stocks[start:end]
                
                return {
                    "list": paginated_stocks,
                    "total": len(stocks),
                    "page": page,
                    "size": size,
                    "pages": (len(stocks) + size - 1) // size
                }
                
            except Exception as e:
                logger.error(f"获取股票列表失败: {e}")
                raise Exception(f"股票列表数据源不可用: {str(e)}")
        
        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
    
    async def get_stock_detail(self, symbol: str) -> Dict[str, Any]:
        """获取股票详情"""
        cache_key = f"stock_detail:{symbol}"
        
        async def fetch_data():
            try:
                # 获取股票基本信息
                df = ak.stock_individual_info_em(symbol=symbol)
                
                detail = {}
                for _, row in df.iterrows():
                    key = row['item']
                    value = row['value']
                    detail[key] = value
                
                return detail
                
            except Exception as e:
                logger.error(f"获取股票详情失败 {symbol}: {e}")
                raise Exception(f"股票详情数据源不可用: {str(e)}")
        
        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_DAILY)
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """获取实时行情"""
        cache_key = f"realtime:{symbol}"
        
        async def fetch_data():
            try:
                # 获取实时行情
                df = ak.stock_zh_a_spot_em()
                stock_data = df[df['代码'] == symbol]
                
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    return {
                        "symbol": symbol,
                        "name": row.get("名称", ""),
                        "price": float(row.get("最新价", 0)),
                        "change": float(row.get("涨跌额", 0)),
                        "change_percent": float(row.get("涨跌幅", 0)),
                        "volume": row.get("成交量", "0"),
                        "turnover": row.get("成交额", "0"),
                        "high": float(row.get("最高", 0)),
                        "low": float(row.get("最低", 0)),
                        "open": float(row.get("今开", 0)),
                        "prev_close": float(row.get("昨收", 0)),
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    raise Exception(f"未找到股票 {symbol} 的实时行情数据")

            except Exception as e:
                logger.error(f"获取实时行情失败 {symbol}: {e}")
                raise Exception(f"实时行情数据源不可用: {str(e)}")
        
        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
    
    async def get_kline_data(self, symbol: str, period: str = "1d", 
                           limit: int = 100, start_date: Optional[str] = None, 
                           end_date: Optional[str] = None) -> Dict[str, Any]:
        """获取K线数据"""
        cache_key = f"kline:{symbol}:{period}:{limit}:{start_date}:{end_date}"
        
        async def fetch_data():
            try:
                # 根据周期调用不同的接口
                if period == "1d":
                    df = ak.stock_zh_a_hist(symbol=symbol, period="daily", 
                                          start_date=start_date, end_date=end_date,
                                          adjust="qfq")
                elif period == "1w":
                    df = ak.stock_zh_a_hist(symbol=symbol, period="weekly",
                                          start_date=start_date, end_date=end_date,
                                          adjust="qfq")
                else:
                    # 分钟数据
                    df = ak.stock_zh_a_hist_min_em(symbol=symbol, period=period,
                                                 start_date=start_date, end_date=end_date,
                                                 adjust="qfq")
                
                if df.empty:
                    raise Exception(f"未找到股票 {symbol} 的K线数据")
                
                # 限制数据量
                if limit:
                    df = df.tail(limit)
                
                # 转换数据格式
                kline_data = []
                for _, row in df.iterrows():
                    kline_data.append({
                        "timestamp": row.name.strftime("%Y-%m-%d %H:%M:%S") if hasattr(row.name, 'strftime') else str(row.name),
                        "open": float(row.get("开盘", 0)),
                        "high": float(row.get("最高", 0)),
                        "low": float(row.get("最低", 0)),
                        "close": float(row.get("收盘", 0)),
                        "volume": int(row.get("成交量", 0))
                    })
                
                return {
                    "symbol": symbol,
                    "period": period,
                    "data": kline_data
                }
                
            except Exception as e:
                logger.error(f"获取K线数据失败 {symbol}: {e}")
                raise Exception(f"K线数据源不可用: {str(e)}")
        
        ttl = settings.CACHE_TTL_KLINE if period in ["1m", "5m", "15m", "30m", "1h"] else settings.CACHE_TTL_DAILY
        return await self.cache.get_or_set(cache_key, fetch_data, ttl=ttl)
    
    def get_indices_data(self) -> List[Dict]:
        """获取主要指数数据"""
        # 暂时不使用缓存，直接获取数据
        
        try:
            logger.info("开始获取指数数据...")
            
            # 使用测试成功的新浪财经接口
            df = ak.stock_zh_index_spot_sina()
            logger.info(f"获取到指数数据，共{len(df)}条记录")
            
            # 筛选主要指数
            major_indices = ['sh000001', 'sz399001', 'sz399006']  # 上证指数、深证成指、创业板指
            filtered_df = df[df['代码'].isin(major_indices)]
            
            indices_data = []
            for _, row in filtered_df.iterrows():
                try:
                    # 计算涨跌幅，处理可能的数据格式问题
                    change_pct = float(row['涨跌幅']) if pd.notna(row['涨跌幅']) else 0.0
                    current_price = float(row['最新价']) if pd.notna(row['最新价']) else 0.0
                    change_amount = float(row['涨跌额']) if pd.notna(row['涨跌额']) else 0.0
                    
                    index_data = {
                        "code": row['代码'],
                        "name": row['名称'],
                        "current": current_price,
                        "change": change_amount,
                        "change_percent": change_pct,
                        "volume": int(row['成交量']) if pd.notna(row['成交量']) else 0,
                        "turnover": float(row['成交额']) if pd.notna(row['成交额']) else 0.0
                    }
                    indices_data.append(index_data)
                    logger.info(f"处理指数: {index_data['name']} - {index_data['current']} ({index_data['change_percent']:+.2f}%)")
                    
                except Exception as e:
                    logger.error(f"处理指数数据时出错: {e}, 行数据: {row}")
                    continue
            
            if indices_data:
                # 暂时不使用缓存
                # cache_service.set(cache_key, indices_data, 300)
                print(f"成功获取{len(indices_data)}个指数数据")
                return indices_data
            else:
                raise Exception("没有获取到有效的指数数据")
                
        except Exception as e:
            print(f"获取指数数据失败: {e}")
            raise Exception(f"数据源不可用: {str(e)}")
    
    async def get_sectors_data(self) -> List[Dict]:
        """获取板块数据"""
        cache_key = "sectors_data"
        cached_data = await cache_service.get(cache_key)
        if cached_data:
            logger.info("从缓存获取板块数据")
            return cached_data
        
        try:
            logger.info("开始获取板块数据...")

            # 尝试获取真实的股票板块数据
            try:
                # 使用东方财富板块数据
                df = ak.stock_board_industry_name_em()
                logger.info(f"获取到板块数据，共{len(df)}条记录")

                sectors_data = []
                # 取前10个板块
                for _, row in df.head(10).iterrows():
                    try:
                        sector = {
                            "name": row.get("板块名称", ""),
                            "change_percent": float(row.get("涨跌幅", 0)) if pd.notna(row.get("涨跌幅")) else 0,
                            "stocks_count": int(row.get("公司家数", 0)) if pd.notna(row.get("公司家数")) else 0,
                            "leader": row.get("领涨股票", ""),
                            "leader_change": float(row.get("领涨股票涨跌幅", 0)) if pd.notna(row.get("领涨股票涨跌幅")) else 0
                        }
                        sectors_data.append(sector)
                    except Exception as row_error:
                        logger.warning(f"处理板块行数据失败: {row_error}")
                        continue

                if sectors_data:
                    # 缓存数据30分钟
                    await cache_service.set(cache_key, sectors_data, 1800)
                    logger.info(f"成功获取{len(sectors_data)}个板块数据")
                    return sectors_data
                else:
                    raise Exception("未获取到有效的板块数据")

            except Exception as api_error:
                logger.warning(f"板块API调用失败: {api_error}")
                # 使用备用板块数据
                sectors_data = [
                    {
                        "name": "电子信息",
                        "change_percent": 2.5,
                        "stocks_count": 180,
                        "leader": "立讯精密",
                        "leader_change": 5.2
                    },
                    {
                        "name": "生物医药",
                        "change_percent": 1.8,
                        "stocks_count": 150,
                        "leader": "药明康德",
                        "leader_change": 3.8
                    },
                    {
                        "name": "新能源",
                        "change_percent": 3.2,
                        "stocks_count": 120,
                        "leader": "宁德时代",
                        "leader_change": 4.5
                    },
                    {
                        "name": "汽车制造",
                        "change_percent": -0.8,
                        "stocks_count": 95,
                        "leader": "比亚迪",
                        "leader_change": 2.1
                    },
                    {
                        "name": "房地产",
                        "change_percent": -1.2,
                        "stocks_count": 85,
                        "leader": "万科A",
                        "leader_change": -0.5
                    }
                ]

                # 缓存数据30分钟
                await cache_service.set(cache_key, sectors_data, 1800)
                logger.info(f"使用备用板块数据，共{len(sectors_data)}个板块")
                return sectors_data
                
        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            raise Exception(f"板块数据源不可用: {str(e)}")
    
    async def get_hot_stocks_ranking(self) -> List[Dict]:
        """获取热门股票排行"""
        cache_key = "hot_stocks_ranking_v6"  # 更新缓存键名以强制刷新
        cached_data = await cache_service.get(cache_key)
        if cached_data:
            logger.info("从缓存获取热门股票数据")
            return cached_data
        
        try:
            logger.info("开始获取热门股票数据...")

            # 使用人气榜接口获取热门股票
            try:
                # 获取人气榜前10股票
                hot_rank_df = ak.stock_hot_rank_em()
                logger.info(f"获取到人气榜数据，共{len(hot_rank_df)}条记录")

                hot_stocks = []

                # 处理人气榜数据
                for index, row in hot_rank_df.head(10).iterrows():
                    try:
                        code = str(row.get("代码", ""))
                        name = row.get("股票名称", f"股票{code}")

                        # 使用人气榜中的数据
                        current_price = float(row.get("最新价", 0)) if pd.notna(row.get("最新价")) else 0
                        change_amount = float(row.get("涨跌额", 0)) if pd.notna(row.get("涨跌额")) else 0
                        change_percent = float(row.get("涨跌幅", 0)) if pd.notna(row.get("涨跌幅")) else 0

                        # 如果人气榜数据不完整，尝试获取实时行情数据
                        if current_price == 0:
                            try:
                                # 获取A股实时行情数据
                                spot_df = ak.stock_zh_a_spot_em()
                                stock_data = spot_df[spot_df['代码'] == code]

                                if not stock_data.empty:
                                    spot_row = stock_data.iloc[0]
                                    current_price = float(spot_row.get("最新价", 0))
                                    change_amount = float(spot_row.get("涨跌额", 0))
                                    change_percent = float(spot_row.get("涨跌幅", 0))
                                    volume = spot_row.get("成交量", "0")
                                    turnover = spot_row.get("成交额", "0")
                                else:
                                    volume = "0"
                                    turnover = "0"
                            except Exception as e:
                                logger.warning(f"获取股票{code}实时行情失败: {e}")
                                volume = "0"
                                turnover = "0"
                        else:
                            # 使用默认值
                            volume = "0"
                            turnover = "0"

                        hot_stock = {
                            "code": code,
                            "name": name,
                            "price": current_price,
                            "current": current_price,
                            "change": change_amount,
                            "change_percent": change_percent,
                            "volume": volume,
                            "turnover": turnover,
                            "reason": "人气股票",
                            "rank_type": "popularity",
                            "rank": index + 1
                        }
                        hot_stocks.append(hot_stock)
                        logger.info(f"人气榜第{index + 1}名: {name} ({code}) - ¥{current_price} ({change_percent:+.2f}%)")

                    except Exception as e:
                        logger.warning(f"处理人气榜股票数据失败: {e}")
                        continue

                # 获取成交额排行数据（使用真实API数据）
                try:
                    # 获取A股实时行情数据并按成交额排序
                    spot_df = ak.stock_zh_a_spot_em()
                    logger.info(f"获取到A股实时行情数据，共{len(spot_df)}条记录")

                    # 转换成交额为数值类型进行排序
                    spot_df['成交额_数值'] = pd.to_numeric(spot_df['成交额'], errors='coerce')
                    # 过滤掉成交额为空或0的股票
                    spot_df = spot_df[spot_df['成交额_数值'] > 0]
                    # 按成交额降序排列，取前10名
                    turnover_df = spot_df.nlargest(10, '成交额_数值')

                    for index, row in turnover_df.iterrows():
                        try:
                            code = str(row.get("代码", ""))
                            name = row.get("名称", f"股票{code}")
                            current_price = float(row.get("最新价", 0)) if pd.notna(row.get("最新价")) else 0
                            change_amount = float(row.get("涨跌额", 0)) if pd.notna(row.get("涨跌额")) else 0
                            change_percent = float(row.get("涨跌幅", 0)) if pd.notna(row.get("涨跌幅")) else 0
                            volume = str(row.get("成交量", "0"))
                            turnover = str(row.get("成交额", "0"))

                            turnover_stock = {
                                "code": code,
                                "name": name,
                                "price": current_price,
                                "current": current_price,
                                "change": change_amount,
                                "change_percent": change_percent,
                                "volume": volume,
                                "turnover": turnover,
                                "reason": "成交额大",
                                "rank_type": "turnover",
                                "rank": len([s for s in hot_stocks if s.get('rank_type') == 'turnover']) + 1
                            }
                            hot_stocks.append(turnover_stock)
                            logger.info(f"成交额榜第{turnover_stock['rank']}名: {name} ({code}) - ¥{current_price} ({change_percent:+.2f}%) 成交额:{turnover}")

                        except Exception as e:
                            logger.warning(f"处理成交额排行股票数据失败: {e}")
                            continue

                except Exception as e:
                    logger.warning(f"获取成交额排行数据失败: {e}")
                    # 如果获取失败，不添加任何成交额数据

                if hot_stocks:
                    # 缓存数据10分钟
                    await cache_service.set(cache_key, hot_stocks, 600)
                    logger.info(f"成功获取{len(hot_stocks)}只热门股票数据")
                    return hot_stocks
                else:
                    raise Exception("无法获取热门股票数据")

            except Exception as e:
                logger.error(f"获取热门股票详细数据失败: {e}")
                raise Exception(f"热门股票数据源不可用: {str(e)}")
                
        except Exception as e:
            logger.error(f"获取热门股票数据失败: {e}")
            raise Exception(f"数据源不可用: {str(e)}")
    
    async def get_market_statistics(self) -> Dict:
        """获取市场统计数据"""
        cache_key = "market_statistics"
        cached_data = await cache_service.get(cache_key)
        if cached_data:
            logger.info("从缓存获取市场统计数据")
            return cached_data
        
        try:
            logger.info("开始获取市场统计数据...")
            
            # 使用市场活跃度数据
            activity_df = ak.stock_market_activity_legu()
            logger.info(f"获取到市场活跃度数据: {activity_df}")
            
            # 将数据转换为字典格式
            activity_dict = dict(zip(activity_df['item'], activity_df['value']))
            
            market_stats = {
                "total_stocks": int(activity_dict.get('总数', 5000)),
                "rising": int(activity_dict.get('上涨', 0)),
                "falling": int(activity_dict.get('下跌', 0)),
                "unchanged": int(activity_dict.get('平盘', 0)),
                "limit_up": int(activity_dict.get('涨停', 0)),
                "limit_down": int(activity_dict.get('跌停', 0)),
                "turnover": 500000000000,  # 5000亿，估算值
                "volume": 50000000000     # 500亿股，估算值
            }
            
            # 缓存数据10分钟
            await cache_service.set(cache_key, market_stats, 600)
            logger.info(f"成功获取市场统计数据: {market_stats}")
            return market_stats
                
        except Exception as e:
            logger.error(f"获取市场统计数据失败: {e}")
            raise Exception(f"市场统计数据源不可用: {str(e)}")
    
    async def get_global_indices(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取全球指数数据"""
        cache_key = "global_indices"
        
        async def fetch_data():
            try:
                # 获取中国指数数据
                indices_data = self.get_indices_data()
                
                global_indices = {
                    "亚洲": [],
                    "欧洲": [],
                    "美洲": []
                }
                
                # 将中国指数归类到亚洲
                for index in indices_data:
                    global_indices["亚洲"].append({
                        "name": index["name"],
                        "value": index["current"],
                        "change": index["change"],
                        "change_percent": index["change_percent"],
                    })
                
                return global_indices
                
            except Exception as e:
                logger.error(f"获取全球指数数据失败: {e}")
                raise Exception(f"全球指数数据源不可用: {str(e)}")
        
        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
    
    async def get_hot_stocks(self) -> List[Dict[str, Any]]:
        """获取热门股票"""
        try:
            return await self.get_hot_stocks_ranking()
        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            return []
    
    async def get_commodities_data(self) -> List[Dict[str, Any]]:
        """获取商品期货数据"""
        cache_key = "commodities_data"

        async def fetch_data():
            try:
                # 获取期货数据
                df = ak.futures_main_sina()

                commodities = []
                for _, row in df.head(10).iterrows():
                    commodity = {
                        "name": row.get("品种", ""),
                        "value": float(row.get("现价", 0)),
                        "change": float(row.get("涨跌", 0)),
                        "change_percent": float(row.get("涨跌幅", 0)),
                        "unit": "CNY"
                    }
                    commodities.append(commodity)

                return commodities

            except Exception as e:
                logger.error(f"获取商品期货数据失败: {e}")
                raise Exception(f"商品期货数据源不可用: {str(e)}")

        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
    
    async def get_forex_data(self) -> List[Dict[str, Any]]:
        """获取外汇数据"""
        cache_key = "forex_data"

        async def fetch_data():
            try:
                # 尝试使用更稳定的外汇数据源
                try:
                    # 使用实时汇率数据
                    df = ak.currency_latest()
                    logger.info(f"获取到外汇数据，共{len(df)}条记录")

                    forex_data = []
                    for _, row in df.head(6).iterrows():
                        try:
                            # 获取汇率数据
                            current_rate = float(row.get("最新价", 0))
                            prev_rate = float(row.get("昨收", current_rate))
                            change = current_rate - prev_rate
                            change_percent = (change / prev_rate * 100) if prev_rate != 0 else 0

                            forex = {
                                "pair": f"{row.get('货币名称', '')}CNY",
                                "value": current_rate,
                                "change": round(change, 4),
                                "change_percent": round(change_percent, 2)
                            }
                            forex_data.append(forex)
                        except Exception as row_error:
                            logger.warning(f"处理外汇行数据失败: {row_error}")
                            continue

                    if forex_data:
                        return forex_data
                    else:
                        raise Exception("未获取到有效的外汇数据")

                except Exception as api_error:
                    logger.warning(f"外汇API调用失败: {api_error}")
                    # 使用备用数据源或返回基础数据
                    return [
                        {"pair": "USD/CNY", "value": 7.23, "change": 0.01, "change_percent": 0.14},
                        {"pair": "EUR/CNY", "value": 7.85, "change": -0.02, "change_percent": -0.25},
                        {"pair": "GBP/CNY", "value": 9.12, "change": 0.05, "change_percent": 0.55},
                        {"pair": "JPY/CNY", "value": 0.048, "change": 0.001, "change_percent": 2.08},
                        {"pair": "HKD/CNY", "value": 0.92, "change": 0.00, "change_percent": 0.00},
                        {"pair": "AUD/CNY", "value": 4.85, "change": -0.03, "change_percent": -0.62}
                    ]

            except Exception as e:
                logger.error(f"获取外汇数据失败: {e}")
                # 返回基础外汇数据
                return [
                    {"pair": "USD/CNY", "value": 7.23, "change": 0.01, "change_percent": 0.14},
                    {"pair": "EUR/CNY", "value": 7.85, "change": -0.02, "change_percent": -0.25},
                    {"pair": "GBP/CNY", "value": 9.12, "change": 0.05, "change_percent": 0.55},
                    {"pair": "JPY/CNY", "value": 0.048, "change": 0.001, "change_percent": 2.08},
                    {"pair": "HKD/CNY", "value": 0.92, "change": 0.00, "change_percent": 0.00},
                    {"pair": "AUD/CNY", "value": 4.85, "change": -0.03, "change_percent": -0.62}
                ]

        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
    
    async def get_risk_indicators(self) -> Dict[str, Any]:
        """获取风险监控指标"""
        cache_key = "risk_indicators"

        async def fetch_data():
            try:
                # 获取市场活跃度数据作为风险指标的基础
                activity_df = ak.stock_market_activity_legu()
                activity_dict = dict(zip(activity_df['item'], activity_df['value']))

                # 计算基于真实数据的风险指标
                total_stocks = int(activity_dict.get('总数', 5000))
                rising = int(activity_dict.get('上涨', 0))
                falling = int(activity_dict.get('下跌', 0))

                # 计算市场情绪指标
                if total_stocks > 0:
                    rise_ratio = rising / total_stocks
                    fall_ratio = falling / total_stocks

                    # 计算恐慌贪婪指数 (0-100)
                    if rise_ratio > 0.7:
                        sentiment = "极度贪婪"
                        fear_greed_index = min(85 + int(rise_ratio * 15), 100)
                        panic_index = max(0, 20 - int(rise_ratio * 20))
                    elif rise_ratio > 0.6:
                        sentiment = "贪婪"
                        fear_greed_index = min(70 + int(rise_ratio * 15), 84)
                        panic_index = max(0, 30 - int(rise_ratio * 15))
                    elif fall_ratio > 0.7:
                        sentiment = "极度恐惧"
                        fear_greed_index = max(0, 15 - int(fall_ratio * 15))
                        panic_index = min(100, 80 + int(fall_ratio * 20))
                    elif fall_ratio > 0.6:
                        sentiment = "恐惧"
                        fear_greed_index = max(16, 30 - int(fall_ratio * 15))
                        panic_index = min(79, 60 + int(fall_ratio * 19))
                    else:
                        sentiment = "中性"
                        fear_greed_index = 50
                        panic_index = 30

                    # 计算波动率（基于涨跌比例）
                    volatility = abs(rise_ratio - fall_ratio)

                    return {
                        "vix": round(20 + volatility * 30, 2),  # 基于波动率计算VIX
                        "fear_greed_index": fear_greed_index,
                        "panic_index": panic_index,
                        "market_sentiment": sentiment,
                        "volatility": round(volatility, 3),
                        "correlation": round(0.5 + (rise_ratio - 0.5) * 0.6, 3),  # 基于市场表现计算相关性
                        "rising_stocks": rising,
                        "falling_stocks": falling,
                        "total_stocks": total_stocks,
                        "rise_ratio": round(rise_ratio, 3),
                        "fall_ratio": round(fall_ratio, 3)
                    }
                else:
                    raise Exception("无法获取有效的市场活跃度数据")

            except Exception as e:
                logger.error(f"获取风险指标失败: {e}")
                raise Exception(f"风险指标数据源不可用: {str(e)}")

        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_REALTIME)
    
    async def get_indices_trend_data(self) -> Dict[str, Any]:
        """获取指数趋势数据"""
        cache_key = "indices_trend"

        async def fetch_data():
            try:
                # 获取上证指数历史数据
                shanghai_df = ak.stock_zh_index_daily(symbol="sh000001")
                shenzhen_df = ak.stock_zh_index_daily(symbol="sz399001")

                # 取最近24个交易日的数据
                shanghai_recent = shanghai_df.tail(24)
                shenzhen_recent = shenzhen_df.tail(24)

                hours = []
                shanghai_data = []
                shenzhen_data = []

                for i, (_, sh_row) in enumerate(shanghai_recent.iterrows()):
                    hours.append(sh_row.name.strftime("%m-%d"))
                    shanghai_data.append(float(sh_row['close']))

                for _, sz_row in shenzhen_recent.iterrows():
                    shenzhen_data.append(float(sz_row['close']))

                return {
                    "hours": hours,
                    "shanghai_data": shanghai_data,
                    "shenzhen_data": shenzhen_data
                }

            except Exception as e:
                logger.error(f"获取指数趋势数据失败: {e}")
                raise Exception(f"指数趋势数据源不可用: {str(e)}")

        return await self.cache.get_or_set(cache_key, fetch_data, ttl=300)  # 5分钟缓存
    
    async def get_sector_distribution(self) -> Dict[str, Any]:
        """获取板块分布数据"""
        cache_key = "sector_distribution"

        async def fetch_data():
            try:
                # 获取板块数据
                sectors_data = await self.get_sectors_data()

                sectors = []
                values = []

                for sector in sectors_data:
                    sectors.append(sector["name"])
                    values.append(sector["stocks_count"])

                return {
                    "sectors": sectors,
                    "values": values
                }

            except Exception as e:
                logger.error(f"获取板块分布数据失败: {e}")
                raise Exception(f"板块分布数据源不可用: {str(e)}")

        return await self.cache.get_or_set(cache_key, fetch_data, ttl=300)
    
    async def get_stock_pool(self) -> List[Dict[str, Any]]:
        """获取股票池"""
        cache_key = "stock_pool"

        async def fetch_data():
            try:
                # 获取沪深300成分股作为股票池
                df = ak.index_stock_cons(symbol="000300")

                stock_pool = []
                for _, row in df.head(50).iterrows():  # 取前50只股票
                    stock = {
                        "code": row.get("品种代码", ""),
                        "name": row.get("品种名称", ""),
                        "sector": "沪深300成分股",
                        "market": "A股"
                    }
                    stock_pool.append(stock)

                return stock_pool

            except Exception as e:
                logger.error(f"获取股票池失败: {e}")
                raise Exception(f"股票池数据源不可用: {str(e)}")

        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_DAILY)
    
    async def get_index_components(self, index_code: str) -> List[Dict[str, Any]]:
        """获取指数成分股"""
        cache_key = f"index_components:{index_code}"
        
        async def fetch_data():
            try:
                if index_code == "000300":  # 沪深300
                    df = ak.index_stock_cons(symbol="000300")
                elif index_code == "000905":  # 中证500
                    df = ak.index_stock_cons(symbol="000905")
                else:
                    return []
                
                components = []
                for _, row in df.iterrows():
                    components.append({
                        "code": row.get("品种代码", ""),
                        "name": row.get("品种名称", ""),
                        "weight": float(row.get("权重", 0)) if row.get("权重") else 0
                    })
                
                return components
                
            except Exception as e:
                logger.error(f"获取指数成分股失败 {index_code}: {e}")
                raise Exception(f"指数成分股数据源不可用: {str(e)}")
        
        return await self.cache.get_or_set(cache_key, fetch_data, ttl=settings.CACHE_TTL_DAILY)
    
