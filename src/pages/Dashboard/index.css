.dashboard {
  height: 100vh;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f1419 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  overflow: hidden;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #ffffff;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  text-align: center;
}

.loading-actions {
  margin-top: 20px;
}

.retry-btn {
  background: linear-gradient(45deg, #00d4aa, #40a9ff);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.retry-btn:active {
  transform: translateY(0);
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #ffffff;
  text-align: center;
}

.error-icon {
  font-size: 64px;
  color: #ff4757;
  margin-bottom: 20px;
}

.error-text {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.error-details {
  font-size: 16px;
  color: #888;
  margin-bottom: 30px;
}

.error-actions {
  margin-top: 20px;
}

/* 数据更新时间 */
.data-update-time {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 12px;
  color: #ffffff;
  opacity: 0.7;
  z-index: 1000;
}

.data-update-time i {
  margin-right: 5px;
}

/* 主要内容区域 */
.dashboard-main {
  display: grid;
  grid-template-columns: 350px 1fr 300px;
  gap: 16px;
  padding: 16px 16px 16px 0;
  height: 100vh;
  overflow: hidden;
}

/* 面板通用样式 */
.panel-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(64, 169, 255, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.panel-section:hover {
  border-color: rgba(64, 169, 255, 0.3);
  box-shadow: 0 8px 32px rgba(64, 169, 255, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(90deg, rgba(64, 169, 255, 0.1) 0%, rgba(0, 212, 170, 0.1) 100%);
  border-bottom: 1px solid rgba(64, 169, 255, 0.1);
  font-size: 14px;
  font-weight: 600;
}

.section-title i {
  color: #40a9ff;
  margin-right: 8px;
}

/* 左侧面板 */
.left-panel {
  overflow-y: auto;
}

/* 市场概览 */
.region-selector {
  display: flex;
  gap: 4px;
}

.region-btn {
  padding: 4px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.region-btn.active,
.region-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: #ffffff;
}

.indices-container {
  padding: 16px 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.index-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.index-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(64, 169, 255, 0.3);
}

.index-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.index-name {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.index-code {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.index-value {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 4px;
}

.index-change {
  font-size: 11px;
  font-weight: 600;
}

.index-change.positive {
  color: #00d4aa;
}

.index-change.negative {
  color: #ff4757;
}

/* 热门股票 */
.stocks-list {
  padding: 16px 20px;
}

.stock-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.stock-item:last-child {
  border-bottom: none;
}

.stock-rank {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #40a9ff 0%, #00d4aa 100%);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  flex-shrink: 0;
}

.stock-info {
  flex: 1;
}

.stock-name {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 2px;
}

.stock-code {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.stock-data {
  text-align: right;
}

.stock-price {
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 2px;
}

.stock-change {
  font-size: 10px;
  font-weight: 600;
  margin-bottom: 2px;
}

.stock-volume {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
}

.stock-change.positive {
  color: #00d4aa;
}

.stock-change.negative {
  color: #ff4757;
}

/* 市场统计 */
.stats-grid {
  padding: 16px 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
}

.stat-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
}

.stat-value.positive {
  color: #00d4aa;
}

.stat-value.negative {
  color: #ff4757;
}

.stat-value.limit-up {
  color: #ff6b6b;
}

.stat-value.limit-down {
  color: #4ecdc4;
}

/* 中间面板 */
.center-panel {
  overflow-y: auto;
}

.chart-section {
  height: 350px;
}

.chart-container {
  padding: 16px 20px;
  height: calc(100% - 60px);
}

/* 板块表现 */
.sectors-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 16px 20px;
}

.sectors-chart {
  display: flex;
  justify-content: center;
  align-items: center;
}

.sectors-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sector-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.sector-info {
  flex: 1;
}

.sector-name {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 2px;
}

.sector-stocks {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.sector-data {
  text-align: right;
}

.sector-change {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.sector-change.positive {
  color: #00d4aa;
}

.sector-change.negative {
  color: #ff4757;
}

.sector-leader {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
}

.sector-leader span:last-child {
  margin-left: 4px;
}

.sector-leader .positive {
  color: #00d4aa;
}

.sector-leader .negative {
  color: #ff4757;
}

/* 右侧面板 */
.right-panel {
  overflow-y: auto;
}

/* 商品期货 */
.commodities-list {
  padding: 16px 20px;
}

.commodity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.commodity-item:last-child {
  border-bottom: none;
}

.commodity-name {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
  flex: 1;
}

.commodity-value {
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  text-align: right;
  margin-right: 8px;
}

.commodity-unit {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
  text-align: right;
  margin-right: 8px;
  width: 60px;
}

.commodity-change {
  font-size: 11px;
  font-weight: 600;
  text-align: right;
  width: 50px;
}

.commodity-change.positive {
  color: #00d4aa;
}

.commodity-change.negative {
  color: #ff4757;
}

/* 外汇市场 */
.forex-list {
  padding: 16px 20px;
}

.forex-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.forex-item:last-child {
  border-bottom: none;
}

.forex-pair {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
  flex: 1;
}

.forex-value {
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  text-align: right;
  margin-right: 12px;
}

.forex-change {
  font-size: 11px;
  font-weight: 600;
  text-align: right;
  width: 50px;
}

.forex-change.positive {
  color: #00d4aa;
}

.forex-change.negative {
  color: #ff4757;
}

/* 风险监控 */
.risk-indicators {
  padding: 16px 20px;
}

.risk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.risk-item:last-child {
  border-bottom: none;
}

.risk-name {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
}

.risk-value {
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  margin-right: 12px;
}

.risk-status {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  text-align: center;
  min-width: 40px;
}

.risk-status.normal {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.risk-status.warning {
  background: rgba(255, 167, 38, 0.2);
  color: #ffa726;
  border: 1px solid rgba(255, 167, 38, 0.3);
}

.risk-status.high {
  background: rgba(255, 71, 87, 0.2);
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .dashboard-main {
    grid-template-columns: 300px 1fr 280px;
  }
  
  .indices-container {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1200px) {
  .dashboard-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .left-panel,
  .center-panel,
  .right-panel {
    overflow-y: visible;
  }
  
  .dashboard {
    overflow-y: auto;
  }
  
  .dashboard-main {
    height: auto;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 12px 16px 12px 0;
    gap: 12px;
  }
  
  .indices-container {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .sectors-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .dashboard-main {
    padding: 8px 12px 8px 0;
  }
  
  .panel-section {
    margin-bottom: 12px;
  }
  
  .section-title {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .indices-container,
  .stocks-list,
  .commodities-list,
  .forex-list,
  .risk-indicators {
    padding: 12px 16px;
  }
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.center-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
  width: 4px;
}

.left-panel::-webkit-scrollbar-track,
.center-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.left-panel::-webkit-scrollbar-thumb,
.center-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
  background: rgba(64, 169, 255, 0.3);
  border-radius: 2px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.center-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 169, 255, 0.5);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.panel-section {
  animation: slideInUp 0.6s ease-out;
}

.panel-section:nth-child(1) { animation-delay: 0.1s; }
.panel-section:nth-child(2) { animation-delay: 0.2s; }
.panel-section:nth-child(3) { animation-delay: 0.3s; }
.panel-section:nth-child(4) { animation-delay: 0.4s; }
.panel-section:nth-child(5) { animation-delay: 0.5s; }

/* 系统状态栏 */
.system-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-right: 20px;
}

.status-item i.fa-circle.online {
  color: #00d4aa;
}

.status-item i.fa-circle.offline {
  color: #ff4757;
}

.status-item i.fa-database.active {
  color: #00d4aa;
}

.status-item i.fa-database.inactive {
  color: #ff4757;
}

.status-item.error {
  color: #ff4757;
}

.refresh-btn {
  background: rgba(0, 212, 170, 0.2);
  border: 1px solid #00d4aa;
  color: #00d4aa;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(0, 212, 170, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn i.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dashboard-title i {
  color: #00d4aa;
  font-size: 32px;
}

.dashboard-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.loading-indicator,
.update-time,
.no-data-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.loading-indicator {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.loading-indicator i {
  color: #00d4aa;
}

.update-time {
  background: rgba(64, 169, 255, 0.2);
  color: #40a9ff;
}

.update-time i {
  color: #40a9ff;
}

.no-data-indicator {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.no-data-indicator i {
  color: #ffc107;
}

.refresh-btn {
  background: linear-gradient(45deg, #00d4aa, #40a9ff);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #00b894, #2d8cf0);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn i {
  font-size: 12px;
}

/* 风险监控新样式 */
.risk-section {
  background: linear-gradient(135deg, rgba(255, 87, 87, 0.1) 0%, rgba(255, 87, 87, 0.05) 100%);
  border: 1px solid rgba(255, 87, 87, 0.2);
}

.risk-gauges {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 16px 20px 0;
}

.risk-summary {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sentiment-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.sentiment-label {
  font-size: 14px;
  color: #cccccc;
  margin-right: 10px;
}

.sentiment-value {
  font-size: 16px;
  font-weight: bold;
  padding: 4px 12px;
  border-radius: 16px;
  text-transform: uppercase;
}

.sentiment-value.极度贪婪,
.sentiment-value.贪婪 {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
}

.sentiment-value.中性 {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
}

.sentiment-value.恐惧,
.sentiment-value.极度恐惧 {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.market-stats-mini {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  color: #cccccc;
}

.market-stats-mini span {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}
