import React from 'react';
import ReactECharts from 'echarts-for-react';

// 示例数据，后续可通过props传递
const indices = [
  { name: '上证指数', value: 3420.57, coord: [121.47, 31.23], change: 1.15 }, // 上海
  { name: '纳斯达克', value: 15000, coord: [-74.00, 40.71], change: 0.8 }, // 纽约
  { name: '日经225', value: 39000, coord: [139.76, 35.68], change: -0.5 }, // 东京
  { name: '恒生指数', value: 18000, coord: [114.17, 22.32], change: 0.3 }, // 香港
  { name: '富时100', value: 8000, coord: [-0.13, 51.51], change: 0.2 }, // 伦敦
  { name: 'DAX', value: 16000, coord: [13.40, 52.52], change: -0.1 }, // 柏林
];

const option = {
  backgroundColor: 'transparent',
  geo: {
    map: 'world',
    roam: true,
    itemStyle: {
      areaColor: '#0b1a2f',
      borderColor: '#3a7ca5',
    },
    emphasis: {
      itemStyle: {
        areaColor: '#1e3a5c',
      },
    },
    zoom: 1.2,
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
  },
  series: [
    {
      name: '全球股指',
      type: 'scatter',
      coordinateSystem: 'geo',
      symbolSize: 18,
      label: {
        show: true,
        formatter: function(params) {
          return `${params.data.name}\n${params.data.value[2]} (${params.data.change > 0 ? '+' : ''}${params.data.change}%)`;
        },
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 12,
        backgroundColor: 'rgba(0,0,0,0.4)',
        padding: 4,
        borderRadius: 4,
      },
      itemStyle: {
        color: function(params) {
          return params.data.change >= 0 ? '#ff4d4f' : '#00c292';
        },
        shadowBlur: 10,
        shadowColor: 'rgba(0,0,0,0.5)',
      },
      data: indices.map(i => ({
        name: i.name,
        value: [...i.coord, i.value],
        change: i.change,
      })),
      zlevel: 2,
    },
  ],
};

export default function WorldMap() {
  return (
    <div style={{ width: '100%', height: 320 }}>
      <ReactECharts
        option={option}
        style={{ width: '100%', height: 320 }}
        opts={{ renderer: 'canvas' }}
        theme="dark"
        notMerge={true}
        lazyUpdate={true}
      />
    </div>
  );
} 