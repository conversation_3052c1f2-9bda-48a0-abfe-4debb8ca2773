import React, { useState } from 'react';
import ReactECharts from 'echarts-for-react';
import './index.css';
import WorldMap from './WorldMap';

const MarketOverview = ({ data }) => {
  const { indices = [] } = data;
  const [activeTimeZone, setActiveTimeZone] = useState('asia');

  const mockIndices = [
    { name: '上证指数', value: 3245.67, change: 12.34, changePercent: 0.38 },
    { name: '深证成指', value: 12456.78, change: -23.45, changePercent: -0.19 },
    { name: '创业板指', value: 2567.89, change: 45.67, changePercent: 1.81 },
    { name: '科创50', value: 1234.56, change: -8.90, changePercent: -0.71 },
    { name: '沪深300', value: 4567.12, change: 34.56, changePercent: 0.76 },
    { name: '中证500', value: 6789.34, change: -12.34, changePercent: -0.18 }
  ];

  return (
    <div className="market-overview">
      {/* 顶部地图展示 */}
      <WorldMap />
      <div className="overview-header">
        <h3>
          <i className="fas fa-globe"></i>
          市场概览
        </h3>
        <div className="market-time">
          <span 
            className={`time-zone ${activeTimeZone === 'asia' ? 'active' : ''}`}
            onClick={() => setActiveTimeZone('asia')}
          >
            亚洲
          </span>
          <span 
            className={`time-zone ${activeTimeZone === 'europe' ? 'active' : ''}`}
            onClick={() => setActiveTimeZone('europe')}
          >
            欧洲
          </span>
          <span 
            className={`time-zone ${activeTimeZone === 'america' ? 'active' : ''}`}
            onClick={() => setActiveTimeZone('america')}
          >
            美洲
          </span>
        </div>
      </div>

      <div className="overview-content">
        <div className="indices-grid">
          {mockIndices.map((index, i) => (
            <div key={i} className="index-card">
              <div className="index-info">
                <h4>{index.name}</h4>
                <div className="index-value">{index.value.toFixed(2)}</div>
                <div className={`index-change ${index.change >= 0 ? 'positive' : 'negative'}`}>
                  {index.change >= 0 ? '+' : ''}{index.change.toFixed(2)} ({index.changePercent >= 0 ? '+' : ''}{index.changePercent.toFixed(2)}%)
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MarketOverview;
