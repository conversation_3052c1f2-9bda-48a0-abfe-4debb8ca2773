import React, { useState } from 'react';
import ReactECharts from 'echarts-for-react';
import './index.css';
import WorldMap from './WorldMap';

const MarketOverview = ({ data }) => {
  const { indices = [] } = data;
  const [activeTimeZone, setActiveTimeZone] = useState('asia');

  // 使用真实数据，如果没有数据则显示加载状态
  const displayIndices = indices.length > 0 ? indices.map(index => ({
    name: index.name,
    value: index.current || 0,
    change: index.change || 0,
    changePercent: index.change_percent || 0
  })) : [];

  return (
    <div className="market-overview">
      {/* 顶部地图展示 */}
      <WorldMap />
      <div className="overview-header">
        <h3>
          <i className="fas fa-globe"></i>
          市场概览
        </h3>
        <div className="market-time">
          <span 
            className={`time-zone ${activeTimeZone === 'asia' ? 'active' : ''}`}
            onClick={() => setActiveTimeZone('asia')}
          >
            亚洲
          </span>
          <span 
            className={`time-zone ${activeTimeZone === 'europe' ? 'active' : ''}`}
            onClick={() => setActiveTimeZone('europe')}
          >
            欧洲
          </span>
          <span 
            className={`time-zone ${activeTimeZone === 'america' ? 'active' : ''}`}
            onClick={() => setActiveTimeZone('america')}
          >
            美洲
          </span>
        </div>
      </div>

      <div className="overview-content">
        <div className="indices-grid">
          {displayIndices.length > 0 ? displayIndices.map((index, i) => (
            <div key={i} className="index-card">
              <div className="index-info">
                <h4>{index.name}</h4>
                <div className="index-value">{(index.value || 0).toFixed(2)}</div>
                <div className={`index-change ${(index.change || 0) >= 0 ? 'positive' : 'negative'}`}>
                  {(index.change || 0) >= 0 ? '+' : ''}{(index.change || 0).toFixed(2)} ({(index.changePercent || 0) >= 0 ? '+' : ''}{(index.changePercent || 0).toFixed(2)}%)
                </div>
              </div>
            </div>
          )) : (
            <div className="loading-message">正在加载指数数据...</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketOverview;
