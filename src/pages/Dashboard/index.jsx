import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import './index.css';
import { useDashboardData, useNetworkStatus, useDataManagerStatus } from '../../hooks/useMarketData';

const Dashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeRegion, setActiveRegion] = useState('亚洲');

  // 使用新的数据管理Hook
  const {
    indices: realIndices,
    hotStocks: realHotStocks,
    marketStats: realMarketStats,
    sectors: realSectors,
    commodities: realCommodities,
    forex: realForex,
    isLoading,
    hasError,
    lastUpdateTime,
    errors,
    refreshAll
  } = useDashboardData();

  const isOnline = useNetworkStatus();
  const dataManagerStatus = useDataManagerStatus();

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 调试：打印原始数据
  console.log('Debug - realIndices:', realIndices);
  console.log('Debug - realHotStocks:', realHotStocks);
  console.log('Debug - realMarketStats:', realMarketStats);
  console.log('Debug - realSectors:', realSectors);
  console.log('Debug - realCommodities:', realCommodities);
  console.log('Debug - realForex:', realForex);

  // 处理指数数据 - 使用真实API数据
  const processedIndices = (realIndices || []).map(index => ({
    name: index.name,
    value: index.current || index.value,
    change: index.change,
    change_percent: index.change_percent,
    code: index.code
  }));

  const globalIndices = {
    '亚洲': processedIndices,
    '欧洲': [],
    '美洲': []
  };

  // 热门股票数据 - 使用真实API数据
  const hotStocks = (realHotStocks || []).map(stock => ({
    name: stock.name,
    code: stock.code,
    price: stock.price || stock.current,
    change: stock.change_percent || stock.change || 0,
    volume: stock.volume || '0'
  }));

  // 商品数据 - 使用真实API数据
  const commodities = realCommodities || [];

  // 外汇数据 - 使用真实API数据
  const forex = realForex || [];

  // 板块数据 - 使用真实API数据
  const sectors = (realSectors || []).map(sector => ({
    name: sector.name,
    change: sector.change_percent || sector.change || 0,
    stocks: sector.stocks_count || sector.stocks || 0,
    leader: sector.lead_stock || sector.leader || '',
    leader_change: sector.lead_change || sector.leader_change || 0
  }));

  // 市场统计 - 使用真实API数据
  const marketStats = realMarketStats || {};

  // 获取指数趋势图
  const getIndexTrendChart = () => {
    const hours = [];
    const shanghaiData = [];
    const shenzhenData = [];
    
    for (let i = 0; i < 24; i++) {
      hours.push(`${i.toString().padStart(2, '0')}:00`);
      shanghaiData.push(3200 + Math.random() * 100 - 50);
      shenzhenData.push(12000 + Math.random() * 500 - 250);
    }

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: ['上证指数', '深证成指'],
        textStyle: { color: '#ffffff' },
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        axisLabel: { color: '#ffffff', fontSize: 10 }
      },
      yAxis: [
        {
          type: 'value',
          name: '上证指数',
          position: 'left',
          axisLabel: { color: '#ffffff', fontSize: 10 },
          nameTextStyle: { color: '#ffffff', fontSize: 10 }
        },
        {
          type: 'value',
          name: '深证成指',
          position: 'right',
          axisLabel: { color: '#ffffff', fontSize: 10 },
          nameTextStyle: { color: '#ffffff', fontSize: 10 }
        }
      ],
      series: [
        {
          name: '上证指数',
          type: 'line',
          yAxisIndex: 0,
          data: shanghaiData,
          smooth: true,
          lineStyle: { color: '#00d4aa', width: 2 },
          symbol: 'none'
        },
        {
          name: '深证成指',
          type: 'line',
          yAxisIndex: 1,
          data: shenzhenData,
          smooth: true,
          lineStyle: { color: '#40a9ff', width: 2 },
          symbol: 'none'
        }
      ]
    };
  };

  // 获取板块分布图
  const getSectorChart = () => {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}% ({d}%)'
      },
      series: [{
        type: 'pie',
        radius: ['30%', '60%'],
        center: ['50%', '50%'],
        data: sectors.map(sector => ({
          name: sector.name,
          value: Math.abs(sector.change),
          itemStyle: { 
            color: sector.change >= 0 ? '#00d4aa' : '#ff4757'
          }
        })),
        label: {
          color: '#ffffff',
          fontSize: 10
        }
      }]
    };
  };

  if (isLoading && !lastUpdateTime) {
    return (
      <div className="dashboard">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">正在加载监控大屏数据...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      {/* 系统状态栏 */}
      <div className="system-status">
        <div className="status-item">
          <i className={`fas fa-circle ${isOnline ? 'online' : 'offline'}`}></i>
          <span>{isOnline ? '在线' : '离线'}</span>
        </div>
        {lastUpdateTime && (
          <div className="status-item">
            <i className="fas fa-clock"></i>
            <span>更新时间: {lastUpdateTime.toLocaleTimeString()}</span>
          </div>
        )}
        <div className="status-item">
          <i className={`fas fa-database ${dataManagerStatus.isActive ? 'active' : 'inactive'}`}></i>
          <span>数据服务: {dataManagerStatus.isActive ? '运行中' : '已停止'}</span>
        </div>
        {hasError && (
          <div className="status-item error">
            <i className="fas fa-exclamation-triangle"></i>
            <span>数据异常</span>
          </div>
        )}
        <button className="refresh-btn" onClick={refreshAll} disabled={isLoading}>
          <i className={`fas fa-sync-alt ${isLoading ? 'spinning' : ''}`}></i>
          刷新数据
        </button>
      </div>
      
      {/* 主要内容区域 */}
      <div className="dashboard-main">
        {/* 左侧区域 */}
        <div className="left-panel">
          {/* 市场概览 */}
          <div className="panel-section market-overview">
            <div className="section-title">
              <i className="fas fa-globe"></i>
              <span>市场概览</span>
              <div className="region-selector">
                {Object.keys(globalIndices).map(region => (
                  <button 
                    key={region}
                    className={`region-btn ${activeRegion === region ? 'active' : ''}`}
                    onClick={() => setActiveRegion(region)}
                  >
                    {region}
                  </button>
                ))}
              </div>
            </div>
            <div className="indices-container">
              {globalIndices[activeRegion].map((index, i) => (
                <div key={i} className="index-item">
                  <div className="index-header">
                    <span className="index-name">{index.name}</span>
                    <span className="index-code">{index.code}</span>
                  </div>
                  <div className="index-value">{(index.value || 0).toFixed(2)}</div>
                  <div className={`index-change ${(index.change || 0) >= 0 ? 'positive' : 'negative'}`}>
                    <span className="change-value">
                      {(index.change || 0) >= 0 ? '+' : ''}{(index.change || 0).toFixed(2)}
                    </span>
                    <span className="change-percent">
                      ({(index.change_percent || index.changePercent || 0) >= 0 ? '+' : ''}{(index.change_percent || index.changePercent || 0).toFixed(2)}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 热门股票 */}
          <div className="panel-section hot-stocks">
            <div className="section-title">
              <i className="fas fa-fire"></i>
              <span>热门股票</span>
            </div>
            <div className="stocks-list">
              {hotStocks.map((stock, i) => (
                <div key={i} className="stock-item">
                  <div className="stock-rank">{i + 1}</div>
                  <div className="stock-info">
                    <div className="stock-name">{stock.name}</div>
                    <div className="stock-code">{stock.code}</div>
                  </div>
                  <div className="stock-data">
                    <div className="stock-price">¥{stock.price}</div>
                    <div className="stock-change positive">+{stock.change}%</div>
                    <div className="stock-volume">{stock.volume}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 市场统计 */}
          <div className="panel-section market-statistics">
            <div className="section-title">
              <i className="fas fa-chart-bar"></i>
              <span>市场统计</span>
            </div>
            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-label">总成交量</div>
                <div className="stat-value">{marketStats.total_volume || marketStats.totalVolume}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">总成交额</div>
                <div className="stat-value">{marketStats.total_value || marketStats.totalValue}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">上涨</div>
                <div className="stat-value positive">{marketStats.rising_stocks || marketStats.risingStocks}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">下跌</div>
                <div className="stat-value negative">{marketStats.falling_stocks || marketStats.fallingStocks}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">涨停</div>
                <div className="stat-value limit-up">{marketStats.limit_up_stocks || marketStats.limitUpStocks}</div>
              </div>
              <div className="stat-item">
                <div className="stat-label">跌停</div>
                <div className="stat-value limit-down">{marketStats.limit_down_stocks || marketStats.limitDownStocks}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 中间区域 */}
        <div className="center-panel">
          {/* 指数走势图 */}
          <div className="panel-section chart-section">
            <div className="section-title">
              <i className="fas fa-chart-line"></i>
              <span>指数走势</span>
            </div>
            <div className="chart-container">
              <ReactECharts 
                option={getIndexTrendChart()} 
                style={{ height: '300px', width: '100%' }}
                theme="dark"
              />
            </div>
          </div>

          {/* 板块表现 */}
          <div className="panel-section sectors-section">
            <div className="section-title">
              <i className="fas fa-layer-group"></i>
              <span>板块表现</span>
            </div>
            <div className="sectors-content">
              <div className="sectors-chart">
                <ReactECharts 
                  option={getSectorChart()} 
                  style={{ height: '200px', width: '100%' }}
                  theme="dark"
                />
              </div>
              <div className="sectors-list">
                {sectors.map((sector, i) => (
                  <div key={i} className="sector-item">
                    <div className="sector-info">
                      <div className="sector-name">{sector.name}</div>
                      <div className="sector-stocks">{sector.stocks}只</div>
                    </div>
                    <div className="sector-data">
                      <div className={`sector-change ${(sector.change || 0) >= 0 ? 'positive' : 'negative'}`}>
                        {(sector.change || 0) >= 0 ? '+' : ''}{(sector.change || 0).toFixed(2)}%
                      </div>
                      <div className="sector-leader">
                        <span>{sector.leader || ''}</span>
                        <span className={(sector.leader_change || sector.leaderChange || 0) >= 0 ? 'positive' : 'negative'}>
                          {(sector.leader_change || sector.leaderChange || 0) >= 0 ? '+' : ''}{(sector.leader_change || sector.leaderChange || 0).toFixed(2)}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧区域 */}
        <div className="right-panel">
          {/* 商品期货 */}
          <div className="panel-section commodities-section">
            <div className="section-title">
              <i className="fas fa-coins"></i>
              <span>商品期货</span>
            </div>
            <div className="commodities-list">
              {commodities.map((commodity, i) => (
                <div key={i} className="commodity-item">
                  <div className="commodity-name">{commodity.name}</div>
                  <div className="commodity-value">{commodity.value}</div>
                  <div className="commodity-unit">{commodity.unit}</div>
                  <div className={`commodity-change ${(commodity.change || 0) >= 0 ? 'positive' : 'negative'}`}>
                    {(commodity.change_percent || commodity.changePercent || 0) >= 0 ? '+' : ''}{(commodity.change_percent || commodity.changePercent || 0).toFixed(2)}%
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 外汇市场 */}
          <div className="panel-section forex-section">
            <div className="section-title">
              <i className="fas fa-exchange-alt"></i>
              <span>外汇市场</span>
            </div>
            <div className="forex-list">
              {forex.map((pair, i) => (
                <div key={i} className="forex-item">
                  <div className="forex-pair">{pair.pair}</div>
                  <div className="forex-value">{(pair.value || 0).toFixed(4)}</div>
                  <div className={`forex-change ${(pair.change || 0) >= 0 ? 'positive' : 'negative'}`}>
                    {(pair.change_percent || pair.changePercent || 0) >= 0 ? '+' : ''}{(pair.change_percent || pair.changePercent || 0).toFixed(2)}%
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 风险监控 */}
          <div className="panel-section risk-section">
            <div className="section-title">
              <i className="fas fa-shield-alt"></i>
              <span>风险监控</span>
            </div>
            <div className="risk-indicators">
              <div className="risk-item">
                <div className="risk-name">VIX恐慌指数</div>
                <div className="risk-value">18.45</div>
                <div className="risk-status normal">正常</div>
              </div>
              <div className="risk-item">
                <div className="risk-name">美债收益率</div>
                <div className="risk-value">4.67%</div>
                <div className="risk-status warning">警告</div>
              </div>
              <div className="risk-item">
                <div className="risk-name">美元指数</div>
                <div className="risk-value">103.45</div>
                <div className="risk-status normal">正常</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
