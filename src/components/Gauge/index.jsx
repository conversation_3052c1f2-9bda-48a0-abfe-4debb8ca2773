import React from 'react';
import ReactECharts from 'echarts-for-react';
import './index.css';

const Gauge = ({ 
  value = 0, 
  title = '', 
  min = 0, 
  max = 100, 
  unit = '', 
  color = '#1890ff',
  size = 'medium',
  showPointer = true,
  showProgress = true
}) => {
  // 根据值确定颜色
  const getColor = (val) => {
    if (val < 30) return '#52c41a'; // 绿色 - 安全
    if (val < 70) return '#faad14'; // 黄色 - 警告
    return '#f5222d'; // 红色 - 危险
  };

  // 根据尺寸确定大小
  const getSizeConfig = () => {
    const configs = {
      small: { width: 150, height: 120, radius: '60%' },
      medium: { width: 200, height: 160, radius: '70%' },
      large: { width: 250, height: 200, radius: '80%' }
    };
    return configs[size] || configs.medium;
  };

  const sizeConfig = getSizeConfig();
  const gaugeColor = color === 'auto' ? getColor(value) : color;

  const option = {
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        startAngle: 200,
        endAngle: -20,
        min: min,
        max: max,
        splitNumber: 5,
        itemStyle: {
          color: gaugeColor,
          shadowColor: 'rgba(0,138,255,0.45)',
          shadowBlur: 10,
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        progress: {
          show: showProgress,
          roundCap: true,
          width: 8
        },
        pointer: {
          show: showPointer,
          icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
          length: '75%',
          width: 16,
          offsetCenter: [0, '5%']
        },
        axisLine: {
          lineStyle: {
            width: 8,
            color: [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        axisTick: {
          distance: -30,
          splitNumber: 5,
          lineStyle: {
            width: 2,
            color: '#999'
          }
        },
        splitLine: {
          distance: -30,
          length: 14,
          lineStyle: {
            width: 3,
            color: '#999'
          }
        },
        axisLabel: {
          distance: -20,
          color: '#999',
          fontSize: 12
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '35%'],
          fontSize: 20,
          fontWeight: 'bolder',
          formatter: `{value}${unit}`,
          color: 'inherit'
        },
        data: [
          {
            value: value
          }
        ]
      }
    ]
  };

  return (
    <div className={`gauge-container gauge-${size}`}>
      {title && <div className="gauge-title">{title}</div>}
      <ReactECharts
        option={option}
        style={{ width: sizeConfig.width, height: sizeConfig.height }}
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

export default Gauge;
