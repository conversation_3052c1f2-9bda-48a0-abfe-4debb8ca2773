.gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.gauge-container:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gauge-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 5px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.gauge-small {
  min-width: 160px;
}

.gauge-medium {
  min-width: 210px;
}

.gauge-large {
  min-width: 260px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gauge-container {
    padding: 8px;
  }
  
  .gauge-title {
    font-size: 12px;
  }
  
  .gauge-large,
  .gauge-medium {
    min-width: 180px;
  }
  
  .gauge-small {
    min-width: 140px;
  }
}

/* 动画效果 */
.gauge-container .echarts-for-react {
  transition: transform 0.3s ease;
}

.gauge-container:hover .echarts-for-react {
  transform: scale(1.02);
}

/* 暗色主题适配 */
.gauge-container {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 0.02) 100%);
}

.gauge-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  padding: 1px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.02) 100%);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}
