// API服务层 - 统一管理所有API请求

const API_BASE_URL = 'http://localhost:8000';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.message || '请求失败');
      }
    } catch (error) {
      console.error(`API请求失败 ${endpoint}:`, error);
      throw error;
    }
  }

  // GET请求
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  // POST请求
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 获取缓存数据
  async getCachedData(dataType) {
    const cacheEndpoints = {
      'indices': '/api/cache/indices',
      'hotStocks': '/api/cache/hot-stocks',
      'marketStats': '/api/cache/market-stats',
      'sectors': '/api/cache/sectors',
      'commodities': '/api/cache/commodities',
      'forex': '/api/cache/forex'
    };

    const endpoint = cacheEndpoints[dataType];
    if (!endpoint) {
      throw new Error(`No cache endpoint for ${dataType}`);
    }

    return this.get(endpoint);
  }

  // PUT请求
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE请求
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // ================================
  // 监控大屏 API
  // ================================

  // 获取监控大屏概览数据
  async getDashboardOverview() {
    return this.get('/api/dashboard/overview');
  }

  // 获取指数趋势数据
  async getIndicesTrend() {
    return this.get('/api/dashboard/indices-trend');
  }

  // ================================
  // 行情中心 API
  // ================================

  // 获取股票列表
  async getStockList(params = {}) {
    const defaultParams = {
      page: 1,
      size: 50,
      sort_by: 'code',
      sort_order: 'asc'
    };
    return this.get('/api/market/stocks', { ...defaultParams, ...params });
  }

  // 获取实时行情
  async getRealtimeQuote(symbol) {
    return this.get(`/api/market/realtime/${symbol}`);
  }

  // 获取K线数据
  async getKlineData(symbol, params = {}) {
    const defaultParams = {
      period: '1d',
      limit: 100
    };
    return this.get(`/api/market/kline/${symbol}`, { ...defaultParams, ...params });
  }

  // 获取指数数据
  async getIndicesData() {
    return this.get('/api/market/indices');
  }

  // 获取板块数据
  async getSectorsData() {
    return this.get('/api/market/sectors');
  }

  // 获取热门股票排行
  async getHotStocks(type = 'gainers') {
    return this.get('/api/market/hot-stocks', { stock_type: type });
  }

  // ================================
  // 交易中心 API
  // ================================

  // 创建订单
  async createOrder(orderData) {
    return this.post('/api/trading/order', orderData);
  }

  // 获取订单列表
  async getOrders(params = {}) {
    const defaultParams = {
      user_id: 1,
      page: 1,
      size: 20
    };
    return this.get('/api/trading/orders', { ...defaultParams, ...params });
  }

  // 取消订单
  async cancelOrder(orderId) {
    return this.put(`/api/trading/order/${orderId}/cancel`);
  }

  // 获取持仓信息
  async getPositions(userId = 1) {
    return this.get('/api/trading/positions', { user_id: userId });
  }

  // 获取交易记录
  async getTransactions(params = {}) {
    const defaultParams = {
      user_id: 1,
      page: 1,
      size: 20
    };
    return this.get('/api/trading/transactions', { ...defaultParams, ...params });
  }

  // 获取账户信息
  async getAccountInfo(userId = 1) {
    return this.get(`/api/trading/account/${userId}`);
  }

  // 获取市场深度
  async getMarketDepth(symbol) {
    return this.get(`/api/trading/market-depth/${symbol}`);
  }

  // ================================
  // 策略中心 API
  // ================================

  // 获取策略列表
  async getStrategies(userId = 1) {
    return this.get('/api/strategy/list', { user_id: userId });
  }

  // 创建策略
  async createStrategy(strategyData) {
    return this.post('/api/strategy/create', strategyData);
  }

  // 更新策略
  async updateStrategy(strategyId, strategyData) {
    return this.put(`/api/strategy/${strategyId}`, strategyData);
  }

  // 启动策略
  async startStrategy(strategyId) {
    return this.post(`/api/strategy/${strategyId}/start`);
  }

  // 停止策略
  async stopStrategy(strategyId) {
    return this.post(`/api/strategy/${strategyId}/stop`);
  }

  // 获取策略表现
  async getStrategyPerformance(strategyId) {
    return this.get(`/api/strategy/${strategyId}/performance`);
  }

  // 获取策略模板
  async getStrategyTemplates() {
    return this.get('/api/strategy/templates');
  }

  // ================================
  // 回测中心 API
  // ================================

  // 创建回测
  async createBacktest(backtestData) {
    return this.post('/api/backtest/create', backtestData);
  }

  // 获取回测列表
  async getBacktests(params = {}) {
    const defaultParams = {
      user_id: 1,
      page: 1,
      size: 20
    };
    return this.get('/api/backtest/list', { ...defaultParams, ...params });
  }

  // 获取回测结果
  async getBacktestResult(backtestId) {
    return this.get(`/api/backtest/${backtestId}/result`);
  }

  // 获取回测分析
  async getBacktestAnalysis(backtestId) {
    return this.get(`/api/backtest/${backtestId}/analysis`);
  }

  // 删除回测
  async deleteBacktest(backtestId) {
    return this.delete(`/api/backtest/${backtestId}`);
  }

  // 对比回测
  async compareBacktests(compareData) {
    return this.post('/api/backtest/compare', compareData);
  }

  // 获取回测报告
  async getBacktestReport(backtestId) {
    return this.get(`/api/backtest/${backtestId}/report`);
  }

  // 获取股票池
  async getStockPool() {
    return this.get('/api/market/stock-pool');
  }

  // 获取指数成分股
  async getIndexComponents(indexCode) {
    return this.get(`/api/market/index-components/${indexCode}`);
  }

  // ================================
  // 用户管理 API
  // ================================

  // 获取用户信息
  async getUserProfile(userId = 1) {
    return this.get('/api/user/profile', { user_id: userId });
  }

  // 更新用户信息
  async updateUserProfile(profileData) {
    return this.put('/api/user/profile', profileData);
  }

  // 获取用户设置
  async getUserSettings(userId = 1) {
    return this.get('/api/user/settings', { user_id: userId });
  }

  // 更新用户设置
  async updateUserSettings(settingsData) {
    return this.put('/api/user/settings', settingsData);
  }

  // 获取自选股
  async getUserWatchlist(userId = 1) {
    return this.get('/api/user/watchlist', { user_id: userId });
  }

  // 添加自选股
  async addToWatchlist(watchlistData) {
    return this.post('/api/user/watchlist', watchlistData);
  }

  // 移除自选股
  async removeFromWatchlist(symbol, userId = 1) {
    return this.delete(`/api/user/watchlist/${symbol}`, { user_id: userId });
  }

  // ================================
  // WebSocket 连接
  // ================================

  // 创建WebSocket连接
  createWebSocket() {
    const wsUrl = `ws://localhost:8000/ws/market`;
    return new WebSocket(wsUrl);
  }
}

// 创建全局API服务实例
const apiService = new ApiService();

export default apiService; 