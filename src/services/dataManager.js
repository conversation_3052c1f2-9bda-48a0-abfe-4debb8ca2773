// 数据管理服务 - 统一管理所有数据获取和更新策略
import apiService from './api';

// 数据更新间隔配置
const UPDATE_INTERVALS = {
  CRITICAL: 10000,    // 10秒 - 指数、热门股票
  IMPORTANT: 30000,   // 30秒 - 板块数据、市场统计  
  NORMAL: 300000,     // 5分钟 - 商品期货、外汇
  LOW: 600000         // 10分钟 - 配置类数据
};

// 交易时间检测
const isTradingTime = () => {
  const now = new Date();
  const hour = now.getHours();
  const minute = now.getMinutes();
  const day = now.getDay();
  
  // 周末不交易
  if (day === 0 || day === 6) return false;
  
  // 交易时间：9:30-11:30, 13:00-15:00
  const morningStart = 9 * 60 + 30;  // 9:30
  const morningEnd = 11 * 60 + 30;   // 11:30
  const afternoonStart = 13 * 60;    // 13:00
  const afternoonEnd = 15 * 60;      // 15:00
  
  const currentTime = hour * 60 + minute;
  
  return (currentTime >= morningStart && currentTime <= morningEnd) ||
         (currentTime >= afternoonStart && currentTime <= afternoonEnd);
};

class DataManager {
  constructor() {
    this.data = {};
    this.timers = {};
    this.subscribers = {};
    this.isActive = false;
    this.retryCount = {};
    this.maxRetries = 3;
  }

  // 启动数据管理器
  start() {
    if (this.isActive) return;
    
    this.isActive = true;
    console.log('DataManager started');
    
    // 立即获取一次数据
    this.fetchAllData();
    
    // 启动定时器
    this.startTimers();
  }

  // 停止数据管理器
  stop() {
    if (!this.isActive) return;
    
    this.isActive = false;
    console.log('DataManager stopped');
    
    // 清除所有定时器
    Object.values(this.timers).forEach(timer => clearInterval(timer));
    this.timers = {};
  }

  // 订阅数据更新
  subscribe(dataType, callback) {
    if (!this.subscribers[dataType]) {
      this.subscribers[dataType] = [];
    }
    this.subscribers[dataType].push(callback);
    
    // 如果已有数据，立即回调
    if (this.data[dataType]) {
      callback(this.data[dataType]);
    }
    
    // 返回取消订阅函数
    return () => {
      this.subscribers[dataType] = this.subscribers[dataType].filter(cb => cb !== callback);
    };
  }

  // 通知订阅者
  notifySubscribers(dataType, data) {
    if (this.subscribers[dataType]) {
      this.subscribers[dataType].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in subscriber callback for ${dataType}:`, error);
        }
      });
    }
  }

  // 更新数据
  updateData(dataType, data) {
    this.data[dataType] = {
      data: data,  // 将实际数据存储在 data 字段中
      updateTime: new Date().toISOString(),
      isLoading: false,
      error: null
    };

    // 重置重试计数
    this.retryCount[dataType] = 0;

    this.notifySubscribers(dataType, this.data[dataType]);
  }

  // 设置错误状态
  setError(dataType, error) {
    this.data[dataType] = {
      ...this.data[dataType],
      isLoading: false,
      error: error.message || '数据获取失败'
    };
    
    this.notifySubscribers(dataType, this.data[dataType]);
  }

  // 设置加载状态
  setLoading(dataType, isLoading = true) {
    if (!this.data[dataType]) {
      this.data[dataType] = {};
    }
    
    this.data[dataType].isLoading = isLoading;
    this.notifySubscribers(dataType, this.data[dataType]);
  }

  // 获取数据的通用方法 - 优先从缓存获取
  async fetchData(dataType, apiCall, retryOnError = true, useCache = true) {
    if (!this.isActive) return;

    try {
      this.setLoading(dataType);

      let data = null;

      // 如果启用缓存，首先尝试从缓存获取
      if (useCache) {
        try {
          const cacheResponse = await apiService.getCachedData(dataType);
          if (cacheResponse && cacheResponse.data) {
            data = cacheResponse.data;
            console.log(`📦 ${dataType} loaded from cache`);
          }
        } catch (cacheError) {
          console.log(`⚠️ Cache miss for ${dataType}, fetching fresh data`);
        }
      }

      // 如果缓存中没有数据，则获取实时数据
      if (!data) {
        data = await apiCall();
        console.log(`🔄 ${dataType} fetched from API`);
      }

      this.updateData(dataType, data);
      console.log(`✅ ${dataType} data updated successfully`);

    } catch (error) {
      console.error(`❌ Failed to fetch ${dataType}:`, error);

      // 重试逻辑
      if (retryOnError && (this.retryCount[dataType] || 0) < this.maxRetries) {
        this.retryCount[dataType] = (this.retryCount[dataType] || 0) + 1;
        console.log(`🔄 Retrying ${dataType} (${this.retryCount[dataType]}/${this.maxRetries})`);

        setTimeout(() => {
          this.fetchData(dataType, apiCall, false, false); // 重试时不使用缓存
        }, 5000); // 5秒后重试

      } else {
        this.setError(dataType, error);
      }
    }
  }

  // 获取所有数据
  async fetchAllData() {
    // 立即获取关键数据
    await Promise.all([
      this.fetchIndicesData(),
      this.fetchHotStocks(),
      this.fetchMarketStats()
    ]);
    
    // 稍后获取次要数据
    setTimeout(() => {
      this.fetchSectorsData();
      this.fetchCommoditiesData();
      this.fetchForexData();
      this.fetchRiskIndicators();
    }, 1000);
  }

  // 启动定时器
  startTimers() {
    // 关键数据定时器
    this.timers.critical = setInterval(() => {
      if (isTradingTime()) {
        this.fetchIndicesData();
        this.fetchHotStocks();
      }
    }, UPDATE_INTERVALS.CRITICAL);

    // 重要数据定时器
    this.timers.important = setInterval(() => {
      this.fetchMarketStats();
      this.fetchSectorsData();
    }, UPDATE_INTERVALS.IMPORTANT);

    // 普通数据定时器
    this.timers.normal = setInterval(() => {
      this.fetchCommoditiesData();
      this.fetchForexData();
    }, UPDATE_INTERVALS.NORMAL);
  }

  // 具体的数据获取方法
  async fetchIndicesData() {
    await this.fetchData('indices', () => apiService.getIndicesData());
  }

  async fetchHotStocks() {
    await this.fetchData('hotStocks', () => apiService.getHotStocks());
  }

  async fetchMarketStats() {
    await this.fetchData('marketStats', () => apiService.getDashboardOverview());
  }

  async fetchSectorsData() {
    await this.fetchData('sectors', () => apiService.getSectorsData());
  }

  async fetchCommoditiesData() {
    await this.fetchData('commodities', () => apiService.get('/api/market/commodities'));
  }

  async fetchForexData() {
    await this.fetchData('forex', () => apiService.get('/api/market/forex'));
  }

  async fetchRiskIndicators() {
    await this.fetchData('riskIndicators', () => apiService.get('/api/market/risk-indicators'));
  }

  // 获取当前数据
  getData(dataType) {
    return this.data[dataType] || { isLoading: true, error: null };
  }

  // 获取所有数据
  getAllData() {
    return this.data;
  }

  // 手动刷新特定数据
  async refreshData(dataType) {
    const methodMap = {
      'indices': this.fetchIndicesData,
      'hotStocks': this.fetchHotStocks,
      'marketStats': this.fetchMarketStats,
      'sectors': this.fetchSectorsData,
      'commodities': this.fetchCommoditiesData,
      'forex': this.fetchForexData,
      'riskIndicators': this.fetchRiskIndicators
    };

    const method = methodMap[dataType];
    if (method) {
      await method.call(this);
    }
  }

  // 获取系统状态
  getStatus() {
    return {
      isActive: this.isActive,
      isTradingTime: isTradingTime(),
      dataCount: Object.keys(this.data).length,
      subscriberCount: Object.keys(this.subscribers).length,
      lastUpdate: Math.max(...Object.values(this.data).map(d => new Date(d.updateTime || 0).getTime()))
    };
  }
}

// 创建全局实例
const dataManager = new DataManager();

export default dataManager;
