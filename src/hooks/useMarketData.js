// 自定义Hook - 简化组件中的市场数据使用
import { useState, useEffect, useCallback } from 'react';
import dataManager from '../services/dataManager';

// 使用单个数据类型的Hook
export const useMarketData = (dataType) => {
  const [data, setData] = useState(() => dataManager.getData(dataType));

  useEffect(() => {
    // 订阅数据更新
    const unsubscribe = dataManager.subscribe(dataType, setData);
    
    // 如果数据管理器未启动，则启动它
    if (!dataManager.isActive) {
      dataManager.start();
    }

    return unsubscribe;
  }, [dataType]);

  // 手动刷新数据
  const refresh = useCallback(() => {
    dataManager.refreshData(dataType);
  }, [dataType]);

  return {
    data: data?.data || null,
    isLoading: data?.isLoading || false,
    error: data?.error || null,
    updateTime: data?.updateTime || null,
    refresh
  };
};

// 使用多个数据类型的Hook
export const useMultipleMarketData = (dataTypes) => {
  const [data, setData] = useState({});

  useEffect(() => {
    const unsubscribers = [];

    dataTypes.forEach(dataType => {
      const unsubscribe = dataManager.subscribe(dataType, (newData) => {
        setData(prevData => ({
          ...prevData,
          [dataType]: newData
        }));
      });
      unsubscribers.push(unsubscribe);
    });

    // 如果数据管理器未启动，则启动它
    if (!dataManager.isActive) {
      dataManager.start();
    }

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, []); // 移除dataTypes依赖，避免无限循环

  // 手动刷新所有数据
  const refreshAll = useCallback(() => {
    dataTypes.forEach(dataType => {
      dataManager.refreshData(dataType);
    });
  }, [dataTypes]);

  // 手动刷新特定数据
  const refresh = useCallback((dataType) => {
    dataManager.refreshData(dataType);
  }, []);

  return {
    data,
    refreshAll,
    refresh
  };
};

// 专门用于指数数据的Hook
export const useIndicesData = () => {
  return useMarketData('indices');
};

// 专门用于热门股票的Hook
export const useHotStocks = () => {
  return useMarketData('hotStocks');
};

// 专门用于市场统计的Hook
export const useMarketStats = () => {
  return useMarketData('marketStats');
};

// 专门用于板块数据的Hook
export const useSectorsData = () => {
  return useMarketData('sectors');
};

// 专门用于商品数据的Hook
export const useCommoditiesData = () => {
  return useMarketData('commodities');
};

// 专门用于外汇数据的Hook
export const useForexData = () => {
  return useMarketData('forex');
};

// 用于监控大屏的综合Hook
export const useDashboardData = () => {
  const dataTypes = ['indices', 'hotStocks', 'marketStats', 'sectors', 'commodities', 'forex', 'riskIndicators'];
  const { data, refreshAll, refresh } = useMultipleMarketData(dataTypes);

  // 获取最新更新时间
  const getLastUpdateTime = () => {
    const updateTimes = Object.values(data)
      .map(item => item?.updateTime)
      .filter(time => time)
      .sort((a, b) => new Date(b) - new Date(a));
    
    return updateTimes.length > 0 ? updateTimes[0] : null;
  };

  return {
    indices: data.indices?.data || [],
    hotStocks: data.hotStocks?.data || [],
    marketStats: data.marketStats?.data || {},
    sectors: data.sectors?.data || [],
    commodities: data.commodities?.data || [],
    forex: data.forex?.data || [],
    riskIndicators: data.riskIndicators?.data || {},
    isLoading: Object.values(data).some(item => item?.isLoading),
    hasError: Object.values(data).some(item => item?.error),
    errors: Object.fromEntries(
      Object.entries(data)
        .filter(([_, item]) => item?.error)
        .map(([key, item]) => [key, item.error])
    ),
    lastUpdateTime: getLastUpdateTime(),
    refreshAll,
    refresh
  };
};

// 数据管理器状态Hook
export const useDataManagerStatus = () => {
  const [status, setStatus] = useState(() => dataManager.getStatus());

  useEffect(() => {
    const interval = setInterval(() => {
      const newStatus = dataManager.getStatus();
      // 只有状态真正改变时才更新
      setStatus(prevStatus => {
        if (JSON.stringify(prevStatus) !== JSON.stringify(newStatus)) {
          return newStatus;
        }
        return prevStatus;
      });
    }, 5000); // 改为5秒更新一次

    return () => clearInterval(interval);
  }, []);

  return status;
};

// 网络状态Hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // 网络恢复时重新启动数据管理器
      if (!dataManager.isActive) {
        dataManager.start();
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      // 网络断开时停止数据管理器
      dataManager.stop();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
};

export default useMarketData;
